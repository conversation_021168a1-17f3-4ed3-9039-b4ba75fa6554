import { useTranslation } from "react-i18next";
import "./Templates.scss";
import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { Button } from "@progress/kendo-react-buttons";
import { useTemplateGridController } from "./hooks/useTemplateGridController";
import TemplatesGridActions from "./components/TemplatesGridActions";
import TemplateDeleteDialog from "./components/TemplateDeleteDialog";
import { useState } from "react";
import AdvancedBaseGrid from "@/components/common/AdvancedBaseGrid/AdvancedBaseGrid";
import { CreateTemplatePopup } from "./components";
import { useCreateTemplatePopup } from "./hooks/useCreateTemplatePopup";
import "./components/CreateTemplatePopup.scss";
import appConfig from "@/config/app.config";

export default function TemplatesPage() {
  const { t } = useTranslation("dashboard");
  const [openDelete, setOpenDelete] = useState(false);
  const handleDeleteDialogClose = () => setOpenDelete(false);
  const {
    columns,
    isColumnsLoading,
    templateList,
    isTemplatesLoading,
    filters,
    pagination,
    sorts,
    handleFilterChange,
    handlePageChange,
    handleRefresh,
    handleSortChange,
    totalRecordCount,
  } = useTemplateGridController();

  const {
    isOpen,
    formData,
    validation,
    isSubmitting,
    openPopup,
    updateField,
    handleCreate,
    handleCancel,
  } = useCreateTemplatePopup();

  return (
    <SectionLayout
      headerActions={
        <Button
          size="small"
          className="header-action-btn"
          icon="add"
          themeColor="base"
          onClick={openPopup}
        >
          {t("btn.createTemplate")}
        </Button>
      }
    >
      <AdvancedBaseGrid
        totalRecordCount={totalRecordCount}
        columns={columns}
        dataSource={templateList}
        filters={filters}
        skip={pagination.skip}
        take={pagination.take}
        onFilterChange={handleFilterChange}
        onPageChange={handlePageChange}
        onSortChange={handleSortChange}
        onRefresh={handleRefresh}
        isLoading={isTemplatesLoading}
        sorts={sorts}
        isColumnsLoading={isColumnsLoading}
        actionsColumn={{
          label: "Actions",
          renderer: (props) => (
            <TemplatesGridActions
              {...props}
              onEdit={() => {}}
              onDelete={() => setOpenDelete(true)}
            />
          ),
        }}
        //dataCellMapper={templateGridDataCellMapper}
      />
      {appConfig.featureFlags.ui.CREATE_TEMPLATE && (
        <CreateTemplatePopup
          isOpen={isOpen}
          formData={formData}
          validation={validation}
          isSubmitting={isSubmitting}
          onFieldChange={updateField}
          onCreate={handleCreate}
          onCancel={handleCancel}
        />
      )}
      <TemplateDeleteDialog
        open={openDelete}
        onClose={handleDeleteDialogClose}
      />
    </SectionLayout>
  );
}
