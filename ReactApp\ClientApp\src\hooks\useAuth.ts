import { useOktaAuth } from "@okta/okta-react";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { setOktaTokens, setOktaUserInfo } from "@/store/oktaSlice";

export function useAuth() {
  const { authState, oktaAuth } = useOktaAuth();
  const dispatch = useDispatch();

  useEffect(() => {
    if (
      authState?.isAuthenticated &&
      authState?.accessToken &&
      authState?.idToken
    ) {
      dispatch(
        setOktaTokens({
          accessToken: authState.accessToken.accessToken,
          idToken: authState.idToken.idToken,
        }),
      );

      dispatch(setOktaUserInfo(authState.idToken.claims));
    }
  }, [authState, dispatch]);

  const login = () => oktaAuth.signInWithRedirect();
  const logout = () => oktaAuth.signOut();

  const user = authState?.isAuthenticated ? authState?.idToken?.claims : null;

  return {
    authState,
    isAuthenticated: !!authState?.isAuthenticated,
    login,
    logout,
    oktaAuth,
    user,
  };
}
