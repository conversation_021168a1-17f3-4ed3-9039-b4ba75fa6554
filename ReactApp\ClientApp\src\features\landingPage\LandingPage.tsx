import AlertBox from "@/components/ui/AlertBox/AlertBox";
import "./LandingPage.scss";
import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { useTranslation } from "react-i18next";
import { useLandingPageController } from "./hooks/useLandingPageController";
import { LandingRecentActivityTable } from "./components/RecentActivityTable";

export default function LandingPage() {
  const { t } = useTranslation("dashboard");

  const {
    showAlert,
    setShowAlert,
    recentActivityData,
    totalRecords,
    dataState,
    isLoadingRecentActivity,
    handleRefreshRecentActivity,
    handleDataStateChange,
    isFetchingRecentActivity,
  } = useLandingPageController();

  return (
    <SectionLayout
      isLoading={isLoadingRecentActivity}
      isFetching={isFetchingRecentActivity}
    >
      <div className="landing-page-content">
        {showAlert && (
          <AlertBox
            message={t("landingPage.alertMessage")}
            onClose={() => setShowAlert(false)}
          />
        )}

        {recentActivityData.length > 0 && (
          <LandingRecentActivityTable
            data={recentActivityData}
            totalRecords={totalRecords}
            dataState={dataState}
            isLoading={isLoadingRecentActivity}
            isFetching={isFetchingRecentActivity}
            onRefresh={handleRefreshRecentActivity}
            onDataStateChange={handleDataStateChange}
          />
        )}
      </div>
    </SectionLayout>
  );
}
