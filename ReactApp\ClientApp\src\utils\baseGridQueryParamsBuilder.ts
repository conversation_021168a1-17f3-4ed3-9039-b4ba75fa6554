import type {
  CompositeFilterDescriptor,
  FilterDescriptor,
  SortDescriptor,
} from "@progress/kendo-data-query";

export const formatDate = (date: string | Date): string =>
  new Date(date).toISOString().split("T")[0];

const isFilterDescriptor = (f: any): f is FilterDescriptor =>
  f && typeof f === "object" && "field" in f && typeof f.field === "string";

const isRangeOperator = (op: string) =>
  ["dateRange", "inRange", "intValueRange"].includes(op);

const getRangeValue = (
  operator: string,
  key: "start" | "end",
  value: any,
): string | null => {
  const val = value?.[key];
  if (val == null) return null;

  return operator === "dateRange" || operator === "inRange"
    ? formatDate(val)
    : String(val);
};

export const baseGridQueryParamsBuilder = (
  skip: number,
  take: number,
  sorts: SortDescriptor[],
  filters: CompositeFilterDescriptor,
): URLSearchParams => {
  const offset = Math.floor(skip / take);
  const params = new URLSearchParams({
    offset: offset.toString(),
    limit: take.toString(),
  });

  const sortString = sorts.map(({ field, dir }) => `${field}:${dir}`).join(",");
  if (sortString) {
    params.append("sort", sortString);
  }

  filters?.filters?.forEach((f) => {
    if (!isFilterDescriptor(f)) return;
    const { field, operator, value } = f;
    if (
      typeof operator === "string" &&
      isRangeOperator(operator) &&
      value &&
      typeof value === "object"
    ) {
      const from = getRangeValue(operator, "start", value);
      const to = getRangeValue(operator, "end", value);
      if (from) params.append(`${field}.From`, from);
      if (to) params.append(`${field}.To`, to);
    } else if (value != null) {
      params.append(field as string, String(value));
    }
  });

  return params;
};
