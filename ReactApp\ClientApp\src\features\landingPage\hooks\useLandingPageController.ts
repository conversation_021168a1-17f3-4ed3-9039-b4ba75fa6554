import { useState } from "react";
import type { SortDescriptor } from "@progress/kendo-data-query";
import { useGetRecentActivity } from "./useGetRecentActivity";

export function useLandingPageController() {
  const [showAlert, setShowAlert] = useState(true);

  // Pagination and sorting state
  const [dataState, setDataState] = useState({
    skip: 0,
    take: 20,
    sort: [{ field: "lastUpdated", dir: "desc" }] as SortDescriptor[],
  });

  // Calculate current page for API call
  const currentPage = Math.floor(dataState.skip / dataState.take) + 1;
  const sortField =
    dataState.sort.length > 0 ? dataState.sort[0].field : "lastUpdated";
  const sortDirection =
    dataState.sort.length > 0 ? dataState.sort[0].dir : "desc";

  const {
    recentActivity: recentActivityResponse,
    isLoading: isLoadingRecentActivity,
    isFetching: isFetchingRecentActivity,
    error: recentActivityError,
    refetchRecentActivity,
  } = useGetRecentActivity({
    pageNumber: currentPage,
    pageSize: dataState.take,
    sortField,
    sortDirection,
  });

  const recentActivityData = recentActivityResponse?.records || [];
  const totalRecords = recentActivityResponse?.totalRecordCount || 0;

  // Handle page change and sorting
  const handleDataStateChange = (event: any) => {
    const newDataState = {
      ...dataState,
      ...event.dataState,
    };

    setDataState(newDataState);
  };

  const handleRefreshRecentActivity = () => {
    refetchRecentActivity();
  };

  return {
    showAlert,
    setShowAlert,
    recentActivityData,
    totalRecords,
    dataState,
    isLoadingRecentActivity,
    isFetchingRecentActivity,
    recentActivityError,
    handleRefreshRecentActivity,
    handleDataStateChange,
  };
}
