import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { vi } from "vitest";
import CreateTemplatePopup from "./CreateTemplatePopup";
import type { CreateTemplateFormData, CreateTemplateValidation } from "../hooks/useCreateTemplatePopup";

// Mock react-i18next
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe("CreateTemplatePopup", () => {
  const mockProps = {
    isOpen: true,
    formData: {
      name: "",
      description: "",
      isActive: true,
    } as CreateTemplateFormData,
    validation: {
      nameError: "",
    } as CreateTemplateValidation,
    isSubmitting: false,
    onFieldChange: vi.fn(),
    onCreate: vi.fn(),
    onCancel: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the popup when isOpen is true", () => {
    render(<CreateTemplatePopup {...mockProps} />);
    
    expect(screen.getByText("Create Template")).toBeInTheDocument();
    expect(screen.getByLabelText("Name*")).toBeInTheDocument();
    expect(screen.getByLabelText("Description")).toBeInTheDocument();
  });

  it("does not render when isOpen is false", () => {
    render(<CreateTemplatePopup {...mockProps} isOpen={false} />);
    
    expect(screen.queryByText("Create Template")).not.toBeInTheDocument();
  });

  it("calls onFieldChange when name input changes", async () => {
    const user = userEvent.setup();
    render(<CreateTemplatePopup {...mockProps} />);
    
    const nameInput = screen.getByLabelText("Name*");
    await user.type(nameInput, "Test Template");
    
    expect(mockProps.onFieldChange).toHaveBeenCalledWith("name", "Test Template");
  });

  it("calls onCreate when Create button is clicked", async () => {
    const user = userEvent.setup();
    render(<CreateTemplatePopup {...mockProps} />);
    
    const createButton = screen.getByText("Create");
    await user.click(createButton);
    
    expect(mockProps.onCreate).toHaveBeenCalled();
  });

  it("calls onCancel when Cancel button is clicked", async () => {
    const user = userEvent.setup();
    render(<CreateTemplatePopup {...mockProps} />);
    
    const cancelButton = screen.getByText("Cancel");
    await user.click(cancelButton);
    
    expect(mockProps.onCancel).toHaveBeenCalled();
  });

  it("shows validation error for name field", () => {
    const propsWithError = {
      ...mockProps,
      validation: {
        nameError: "Template Name is mandatory",
      },
    };
    
    render(<CreateTemplatePopup {...propsWithError} />);
    
    expect(screen.getByText("Template Name is mandatory")).toBeInTheDocument();
  });

  it("disables buttons when submitting", () => {
    render(<CreateTemplatePopup {...mockProps} isSubmitting={true} />);
    
    expect(screen.getByText("Creating...")).toBeInTheDocument();
    expect(screen.getByText("Creating...")).toBeDisabled();
    expect(screen.getByText("Cancel")).toBeDisabled();
  });
});
