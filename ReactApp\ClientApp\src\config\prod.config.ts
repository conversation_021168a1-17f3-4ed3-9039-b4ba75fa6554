import defaultAppConfig from "./app.config";

export const prodConfig = {
  ...defaultAppConfig,
  env: "production",
  baseUrl: import.meta.env.VITE_BASE_URL,
  X_API_KEY: import.meta.env.VITE_X_API_KEY,
  keycloak: {
    keycloakUrl: import.meta.env.VITE_KEYCLOAK_URL,
    realm: import.meta.env.VITE_KEYCLOAK_REALM,
    clientId: import.meta.env.VITE_KEYCLOAK_CLIENT_ID,
  },
  featureFlags: {
    ...defaultAppConfig.featureFlags,
    TERMS_API_USE_MOCK: false,
    RECENT_ACTIVITY_API_USE_MOCK: false,
  },
};

export default prodConfig;
