import { CheckCircleIcon, NoSymbolIcon } from "@heroicons/react/24/outline";
import { TEMPLATE_STATUS } from "../constants/status";

const statusConfig: Record<
  string,
  { icon: React.ElementType; color: string; label: string }
> = {
  Active: {
    icon: CheckCircleIcon,
    color: "green",
    label: TEMPLATE_STATUS.ACTIVE,
  },
  Inactive: {
    icon: NoSymbolIcon,
    color: "red",
    label: TEMPLATE_STATUS.INACTIVE,
  },
};

export const StatusCell = ({ dataItem, field }: any) => {
  const value = dataItem[field];
  const config = statusConfig[value];

  if (!config) return <td>{value}</td>;

  const Icon = config.icon;

  return (
    <td>
      <span
        style={{
          display: "flex",
          alignItems: "center",
          gap: 4,
          color: config.color,
        }}
      >
        <Icon style={{ width: 18, height: 18 }} />
        <span>{config.label}</span>
      </span>
    </td>
  );
};

export const DateCell = ({ dataItem, field }: any) => {
  const value = dataItem[field];

  if (!value) return <td />;

  const date = new Date(value);
  const formattedDate = date.toLocaleDateString("en-GB");

  return <td>{formattedDate}</td>;
};
