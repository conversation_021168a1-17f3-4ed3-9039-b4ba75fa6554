import React from "react";
import { PanelBarItem } from "@progress/kendo-react-layout";
import { SvgIcon } from "@progress/kendo-react-common";
import type { SVGIcon } from "@progress/kendo-react-common";
import { caretAltDownIcon, caretAltUpIcon } from "@progress/kendo-svg-icons";

interface SidebarMenuItemProps {
  id: string;
  title: string;
  icon: React.ElementType;
  expanded: boolean;
  onToggle: (_id: string) => void;
  collapsed: boolean;
  childrenItems: React.ReactNode;
}

const getCaretIcon = (expanded: boolean): SVGIcon =>
  expanded ? caretAltUpIcon : caretAltDownIcon;

export default function SidebarMenuItem({
  id,
  title,
  icon: MenuIcon,
  expanded,
  onToggle,
  collapsed,
  childrenItems,
}: SidebarMenuItemProps) {
  return (
    <PanelBarItem
      id={id}
      expanded={expanded}
      className="custom-panelbar-item"
      title={
        <div
          className={`menu-title ${expanded ? "expanded" : ""}`}
          onClick={() => onToggle(id)}
        >
          <SvgIcon
            icon={getCaretIcon(expanded)}
            className="caret-placeholder"
          />
          <MenuIcon className="menu-heroicon" />
          <span className="menu-text">{title}</span>
          {!collapsed && (
            <span className="menu-count">
              ({Array.isArray(childrenItems) ? childrenItems.length : 0})
            </span>
          )}
        </div>
      }
    >
      {childrenItems}
    </PanelBarItem>
  );
}
