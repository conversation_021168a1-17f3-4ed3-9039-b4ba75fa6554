import { useUpdateSampleMutation } from "@/api/sampleApiSlice";
import logger from "@/utils/logger";

export const useUpdateSampleFeature = () => {
  const [updateSample, { isLoading }] = useUpdateSampleMutation();

  const update = async (
    payload: any,
    onError: (_msg: string) => void,
    onSuccess?: (_msg?: string) => void,
  ) => {
    try {
      const res = await updateSample(payload).unwrap();
      logger.info("Update success", res);
      onSuccess?.(res.message);
    } catch (error: any) {
      logger.error("Update failed", error);
      onError(error?.data?.message || "Update failed.");
    }
  };

  return { update, isUpdating: isLoading };
};
