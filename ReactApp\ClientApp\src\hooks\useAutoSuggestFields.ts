// hooks/useAutoSuggestField.ts
import { useCallback, useState } from "react";
import { useDebounce } from "use-debounce";

type FetchSuggestionsFn = (_field: string, _value: string) => Promise<string[]>;

export function useAutoSuggestField(
  field: string,
  fetchSuggestions: FetchSuggestionsFn,
) {
  const [input, setInput] = useState("");
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [debouncedInput] = useDebounce(input, 500);

  const loadSuggestions = useCallback(async () => {
    if (!debouncedInput.trim()) {
      setSuggestions([]);
      return;
    }

    setIsLoading(true);
    try {
      const result = await fetchSuggestions(field, debouncedInput);
      setSuggestions(result);
    } catch {
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  }, [debouncedInput, field, fetchSuggestions]);

  return {
    input,
    setInput,
    suggestions,
    isLoading,
    loadSuggestions,
    debouncedInput,
  };
}
