import { Button } from "@progress/kendo-react-buttons";
import { useLazyDownloadAuditTrailQuery } from "@/api/auditTrailsApiSlice";
import type { AuditTrail } from "@/types/auditTrails";

interface ExportCsvFileProps {
  dataItem: AuditTrail;
}

export default function ExportCsvFile({ dataItem }: ExportCsvFileProps) {
  const [triggerDownload] = useLazyDownloadAuditTrailQuery();

  const handleDownload = async () => {
    const blob = await triggerDownload(String(dataItem.id)).unwrap();
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `${dataItem.fileName || "terms"}_${dataItem.id}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const hasAcceptanceHistory =
    typeof dataItem.acceptanceHistory === "string" &&
    dataItem.acceptanceHistory.trim() !== "";

  return (
    <td className="k-command-cell">
      <Button
        onClick={handleDownload}
        themeColor="primary"
        fillMode="flat"
        disabled={!hasAcceptanceHistory}
      >
        {hasAcceptanceHistory ? "Export CSV" : "__"}
      </Button>
    </td>
  );
}
