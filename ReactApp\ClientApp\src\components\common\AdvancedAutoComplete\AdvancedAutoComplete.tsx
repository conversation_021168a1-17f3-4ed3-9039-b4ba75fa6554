// AdvancedAutoComplete.tsx
import {
  AutoComplete,
  type AutoCompleteChangeEvent,
} from "@progress/kendo-react-dropdowns";
import { useEffect, useState } from "react";
import type { GridCustomFilterCellProps } from "@progress/kendo-react-grid";
import { Button } from "@progress/kendo-react-buttons";
import { filterClearIcon } from "@progress/kendo-svg-icons";
import { useAutoSuggestField } from "@/hooks/useAutoSuggestFields";

interface AdvancedAutoCompleteProps extends GridCustomFilterCellProps {
  field: string;
  useAutoSuggestHook: () => {
    fetchSuggestions: (_field: string, _value: string) => Promise<string[]>;
    isLoading: boolean;
  };
}

export default function AdvancedAutoComplete({
  value,
  onChange,
  field,
  useAutoSuggestHook,
}: AdvancedAutoCompleteProps) {
  const [openPopup, setOpenPopup] = useState(false);

  const { fetchSuggestions, isLoading } = useAutoSuggestHook();

  const { input, setInput, suggestions, loadSuggestions, debouncedInput } =
    useAutoSuggestField(field, fetchSuggestions);

  const handleChange = (e: AutoCompleteChangeEvent) => {
    setInput(e.value);
    setOpenPopup(true);
  };

  const handleSelect = (val: string) => {
    onChange({
      value: val,
      operator: "contains",
      syntheticEvent: undefined as any,
    });
    setOpenPopup(false);
  };

  const handleClear = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    setInput("");
    onChange({ value: null, operator: "", syntheticEvent: event });
    setOpenPopup(false);
  };

  useEffect(() => {
    loadSuggestions();
  }, [debouncedInput, loadSuggestions]);

  return (
    <div className="gridCellFilterContainer">
      <AutoComplete
        data={suggestions}
        value={input}
        onChange={(e) => {
          handleChange(e);
          if (suggestions.includes(e.value)) {
            handleSelect(e.value);
          }
        }}
        onFocus={() => suggestions.length && setOpenPopup(true)}
        onBlur={() => setOpenPopup(false)}
        popupSettings={{ show: openPopup, animate: true }}
        placeholder="Search..."
        suggest
        loading={isLoading}
        size="medium"
      />
      <Button
        svgIcon={filterClearIcon}
        title="Clear"
        type="button"
        onClick={handleClear}
        disabled={!value}
      />
    </div>
  );
}
