// components/BaseGridFilterFactory.tsx
import type { FC } from "react";
import { HeaderTdElement } from "@progress/kendo-react-data-tools";
import type { GridCustomFilterCellProps } from "@progress/kendo-react-grid";
import type { ColumnConfig } from "@/types/column";
import AdvancedAutoComplete from "@/components/common/AdvancedAutoComplete/AdvancedAutoComplete";
import MultiSelector from "@/components/common/MultiSelector/MultiSelector";
import FileSizeSelector from "@/components/common/FileSizeSelector/FileSizeSelector";
import DateRangeSelector from "@/components/common/DateRangeSelector/DateRangeSelector";

interface Props extends GridCustomFilterCellProps {
  column: ColumnConfig;
  useAutoSuggestHook?: () => {
    fetchSuggestions: (_field: string, _value: string) => Promise<string[]>;
    isLoading: boolean;
  };
}

const BaseGridFilterFactory: FC<Props> = ({
  column,
  useAutoSuggestHook,
  ...props
}) => {
  const { columnId } = props.thProps || {};
  const { key, filterType, filterData = [] } = column;

  const renderComponent = () => {
    switch (filterType) {
      case "search":
        return (
          <AdvancedAutoComplete
            {...props}
            field={key}
            useAutoSuggestHook={useAutoSuggestHook!}
          />
        );
      case "multiSelect":
        return (
          <MultiSelector
            {...props}
            data={filterData}
            dataTextField={Object.keys(filterData[0] || {})[0] || "name"}
            dataValueField={Object.keys(filterData[0] || {})[1] || "value"}
          />
        );
      case "range":
        if (key === "fileSize") return <FileSizeSelector {...props} />;
        if (key.includes("Date") || key.includes("On"))
          return <DateRangeSelector {...props} />;
        return null;
      default:
        return null;
    }
  };

  return (
    <HeaderTdElement columnId={columnId || ""} {...props.thProps}>
      {renderComponent()}
    </HeaderTdElement>
  );
};

export default BaseGridFilterFactory;
