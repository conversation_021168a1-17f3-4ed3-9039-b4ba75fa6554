import "@progress/kendo-font-icons/dist/index.css";
import "@/styles/kendo-theme/scss/index.scss";

import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";

import { Provider } from "react-redux";
import { store } from "./store/store";

import { Security } from "@okta/okta-react";
import { OktaAuth } from "@okta/okta-auth-js";
import oktaConfig from "./oktaConfig";
import "@/utils/i18n";
import { BrowserRouter } from "react-router-dom";

const oktaAuth = new OktaAuth({
  issuer: oktaConfig.issuer,
  clientId: oktaConfig.clientId,
  redirectUri: oktaConfig.redirectUri,
  postLogoutRedirectUri: oktaConfig.postLogoutRedirectUri,
  scopes: oktaConfig.scopes,
  tokenManager: {
    autoRenew: oktaConfig.tokenManager?.autoRenew ?? true,
    secure: oktaConfig.tokenManager?.secure ?? true,
    storageKey: oktaConfig.tokenManager?.storageKey ?? "AP_AUTH_TOKEN",
  },
});

// Ensure Okta SDK initializes before rendering the app
(async () => {
  await oktaAuth.start();

  ReactDOM.createRoot(document.getElementById("root")!).render(
    <React.StrictMode>
      <Provider store={store}>
        <BrowserRouter>
          <Security
            oktaAuth={oktaAuth}
            restoreOriginalUri={async (_oktaAuth, originalUri) => {
              window.location.replace(originalUri || "/dashboard");
            }}
          >
            <App />
          </Security>
        </BrowserRouter>
      </Provider>
    </React.StrictMode>,
  );
})();
