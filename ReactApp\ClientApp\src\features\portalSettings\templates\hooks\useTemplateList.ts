import { useGetTemplateListQuery } from "@/api/templatesApiSlice";
import type {
  CompositeFilterDescriptor,
  SortDescriptor,
} from "@progress/kendo-data-query";
import { useMemo } from "react";

interface UseTemplateListProps {
  skip: number;
  take: number;
  filters: CompositeFilterDescriptor;
  sorts: SortDescriptor[];
}

export const useTemplateList = ({
  skip,
  take,
  filters,
  sorts,
}: UseTemplateListProps) => {
  const { data, isLoading, isError, error } = useGetTemplateListQuery({
    skip,
    take,
    filters,
    sorts,
  });

  const templateList = useMemo(() => data?.records || [], [data]);
  const totalRecordCount = useMemo(() => data?.totalRecordCount || 0, [data]);

  return {
    templateList,
    totalRecordCount,
    isLoading,
    isError,
    error,
  };
};
