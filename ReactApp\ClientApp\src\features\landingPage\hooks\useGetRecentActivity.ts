import { useGetRecentActivityQuery } from "@/api/recentActivityApiSlice";
import type { RecentActivityParams } from "@/types/recentActivity";

export const useGetRecentActivity = (params: RecentActivityParams) => {
  const {
    data: recentActivity,
    isLoading,
    isFetching,
    error,
    refetch: refetchRecentActivity,
  } = useGetRecentActivityQuery(params, {
    refetchOnMountOrArgChange: true,
  });

  return {
    recentActivity,
    isLoading,
    isFetching,
    error,
    refetchRecentActivity,
  };
};
