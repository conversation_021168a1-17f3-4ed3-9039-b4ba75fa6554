import { useRoutes, Navigate } from "react-router-dom";
import { ROUTES } from "@/constants/routes";
import { authRoutes } from "./authRoutes";
import { dashboardRoutes } from "./dashboardRoutes";
import { devRoutes } from "./devRoutes";

const AppRoutes = () => {
  const routes = [
    {
      path: ROUTES.ROOT,
      element: <Navigate to={ROUTES.DASHBOARD} replace />,
    },
    ...authRoutes,
    ...dashboardRoutes,
    ...devRoutes,
    {
      path: "*",
      element: <Navigate to={ROUTES.DASHBOARD} replace />,
    },
  ];

  return useRoutes(routes);
};

export default AppRoutes;
