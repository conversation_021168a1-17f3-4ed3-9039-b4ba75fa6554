import React, { useEffect } from "react";
import { useOktaAuth } from "@okta/okta-react";

interface RequireAuthProps {
  children: React.ReactElement;
}

export default function RequireAuth({ children }: RequireAuthProps) {
  const { authState, oktaAuth } = useOktaAuth();

  useEffect(() => {
    if (authState?.isAuthenticated === false) {
      oktaAuth.signInWithRedirect({ originalUri: window.location.pathname });
    }
  }, [authState, oktaAuth]);

  if (!authState || authState.isPending) {
    return <div>Loading...</div>;
  }

  return authState.isAuthenticated ? children : null;
}
