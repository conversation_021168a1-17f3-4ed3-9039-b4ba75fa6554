import { Avatar } from "@progress/kendo-react-layout";
import { Icon } from "@progress/kendo-react-common";
import { Popup } from "@progress/kendo-react-popup";
import { useTranslation } from "react-i18next";
import { useAuth } from "@/hooks/useAuth";
import { useUserMenu } from "./useUserMenu";
import styles from "./TopBar.module.scss";

export default function UserMenu() {
  const { t } = useTranslation();
  const { user, logout } = useAuth();
  const username = user?.name || "User";

  const { showPopup, userRef, popupRef, togglePopup, closePopup } =
    useUserMenu();

  const handleLogoutClick = () => {
    closePopup();
    logout();
  };

  return (
    <>
      <button
        type="button"
        className={`${styles["topbar__item"]} ${styles["topbar__item--user"]}`}
        ref={userRef}
        onClick={togglePopup}
        aria-haspopup="true"
        aria-expanded={showPopup}
        aria-controls="user-menu-popup"
      >
        <Avatar type="icon" size="small">
          <Icon name="user" />
        </Avatar>
        <span>{username}</span>
      </button>

      <Popup
        anchor={userRef.current}
        show={showPopup}
        popupClass={styles["topbar__popup"]}
        anchorAlign={{ horizontal: "left", vertical: "bottom" }}
        popupAlign={{ horizontal: "left", vertical: "top" }}
      >
        <div ref={popupRef} id="user-menu-popup" role="menu">
          <div
            className={styles["topbar__popup-item"]}
            role="menuitem"
            tabIndex={0}
            onClick={handleLogoutClick}
            onKeyDown={(e) => {
              if (e.key === "Enter") handleLogoutClick();
            }}
          >
            <Icon name="logout" style={{ marginRight: 6 }} />
            {t("logout")}
          </div>
        </div>
      </Popup>
    </>
  );
}
