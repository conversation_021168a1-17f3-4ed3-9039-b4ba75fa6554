import { Checkbox, RadioButton } from "@progress/kendo-react-inputs";
import { useTranslation } from "react-i18next";

interface TriggerPoints {
  promptAfterFirstLogin: boolean;
  promptWhenUpdated: boolean;
  promptAnnually: boolean;
  promptQuarterly: boolean;
}

interface Props {
  values: TriggerPoints;
  onChange: (_updated: TriggerPoints) => void;
}

export default function ConditionsForm({ values, onChange }: Props) {
  const { t } = useTranslation("dashboard");

  const handleCheckboxChange = (key: keyof TriggerPoints) => {
    onChange({ ...values, [key]: !values[key] });
  };

  const handleRadioChange = (value: "Annually" | "Quarterly") => {
    onChange({
      ...values,
      promptAnnually: value === "Annually",
      promptQuarterly: value === "Quarterly",
    });
  };

  return (
    <div className="conditions-form">
      <div className="conditions-header">
        {t("conditions.title")} <span className="required">*</span><div className="conditions-subtext">{t("conditions.subtext")}</div>
      </div>
      

      <div className="conditions-options">
        {/* Disabled checkbox */}
        <Checkbox
          checked={values.promptAfterFirstLogin}
          disabled
          label={
            <span className="option-label">{t("conditions.firstLogin")}</span>
          }
        />

        {/* Editable checkbox */}
        <Checkbox
          checked={values.promptWhenUpdated}
          onChange={() => handleCheckboxChange("promptWhenUpdated")}
          label={
            <span className="option-label">{t("conditions.updated")}</span>
          }
        />

        {/* Radio buttons - mutually exclusive */}
        <div className="radio-label">
          <RadioButton
            name="displayFrequency"
            value="Annually"
            checked={values.promptAnnually}
            onChange={() => handleRadioChange("Annually")}
            label=""
          />
          <span>{t("conditions.annually")}</span>
        </div>

        <div className="radio-label">
          <RadioButton
            name="displayFrequency"
            value="Quarterly"
            checked={values.promptQuarterly}
            onChange={() => handleRadioChange("Quarterly")}
            label=""
          />
          <span>{t("conditions.quarterly")}</span>
        </div>
      </div>
    </div>
  );
}
