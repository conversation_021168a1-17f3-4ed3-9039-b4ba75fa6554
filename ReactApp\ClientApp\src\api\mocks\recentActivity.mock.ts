import type {
  RecentActivity,
  RecentActivityResponse,
} from "@/types/recentActivity";

export const mockRecentActivityData: RecentActivity[] = [
  {
    id: "1",
    setupArea: "DMS Settings",
    section: "Privileges Settings",
    updatedBy: "<PERSON>",
    lastUpdated: "2025/05/22",
  },
  {
    id: "2",
    setupArea: "DMS Settings",
    section: "Staff Settings",
    updatedBy: "<PERSON>",
    lastUpdated: "2025/05/22",
  },
  {
    id: "3",
    setupArea: "DMS Settings",
    section: "AutoFiling Settings",
    updatedBy: "<PERSON>",
    lastUpdated: "2025/05/22",
  },
  {
    id: "4",
    setupArea: "Portal Settings",
    section: "Terms & Conditions",
    updatedBy: "<PERSON>",
    lastUpdated: "2025/01/24",
  },
  {
    id: "5",
    setupArea: "Portal Settings",
    section: "Folder Permissions",
    updatedBy: "<PERSON>",
    lastUpdated: "2025/01/24",
  },
  {
    id: "6",
    setupArea: "Shared Settings",
    section: "Client Branding",
    updatedBy: "<PERSON>",
    lastUpdated: "2025/01/24",
  },
  {
    id: "7",
    setupArea: "Shared Settings",
    section: "Email Format Settings",
    updatedBy: "Tarquin Guthrie",
    lastUpdated: "2025/01/24",
  },
  {
    id: "8",
    setupArea: "DMS Settings",
    section: "Privileges Settings",
    updatedBy: "Nancy Drew",
    lastUpdated: "2025/03/11",
  },
  {
    id: "9",
    setupArea: "Portal Settings",
    section: "Terms & Conditions",
    updatedBy: "Samuel Kim",
    lastUpdated: "2025/04/28",
  },
  {
    id: "10",
    setupArea: "Shared Settings",
    section: "Client Branding",
    updatedBy: "Lewis Carter",
    lastUpdated: "2025/05/03",
  },
  {
    id: "11",
    setupArea: "DMS Settings",
    section: "AutoFiling Settings",
    updatedBy: "Hannah Cole",
    lastUpdated: "2025/02/14",
  },
  {
    id: "12",
    setupArea: "Portal Settings",
    section: "Folder Permissions",
    updatedBy: "Arnold Jefferson",
    lastUpdated: "2025/01/17",
  },
  {
    id: "13",
    setupArea: "Shared Settings",
    section: "Email Format Settings",
    updatedBy: "Jane Robinson",
    lastUpdated: "2025/03/29",
  },
  {
    id: "14",
    setupArea: "DMS Settings",
    section: "Staff Settings",
    updatedBy: "Tarquin Guthrie",
    lastUpdated: "2025/05/04",
  },
  {
    id: "15",
    setupArea: "Portal Settings",
    section: "Terms & Conditions",
    updatedBy: "Nancy Drew",
    lastUpdated: "2025/04/15",
  },
];

export const mockRecentActivityResponse: RecentActivityResponse = {
  records: mockRecentActivityData,
  pageCount: 1,
  pageNumber: 1,
  pageSize: 20,
  totalRecordCount: mockRecentActivityData.length,
};
