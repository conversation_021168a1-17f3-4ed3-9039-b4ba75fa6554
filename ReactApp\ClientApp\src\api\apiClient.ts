import axios from "axios";
import config from "@/config";

const apiClient = axios.create({
  baseURL: config.baseUrl,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request Interceptor (e.g., auth)
apiClient.interceptors.request.use((request) => {
  // Example: attach token if exists
  // request.headers.Authorization = `Bearer ${getToken()}`;
  return request;
});

// Response Interceptor
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Optional: global error logging
    return Promise.reject(error);
  },
);

export default apiClient;
