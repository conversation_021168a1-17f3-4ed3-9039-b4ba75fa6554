import config from "@/config";
import apiClient from "@/api/apiClient";
import logger from "@/utils/logger";

export async function fetchTermsPdfBlob(termsId: number): Promise<Blob> {
  const db = config.temporary.db || "NTB1"; // Example database identifier, will be removed after discovery API implementation
  const authorization = config.temporary.authorization; // Example authorization token, will be removed after discovery API implementation

  if (config.featureFlags.api.DOWNLOAD_TERMS_PDF) {
    await new Promise((res) => setTimeout(res, 500));
    const { mockDownloadPdfBlob } = await import("@/api/mocks/terms.mock");
    return mockDownloadPdfBlob;
  }

  try {
    // const token = localStorage.getItem("access_token") || ""; // may be after discovery API implementation
    const response = await apiClient.get(
      `${db}/api/terms-and-conditions/${termsId}/download`,
      {
        responseType: "blob",
        headers: {
          Accept: "application/pdf",
          Authorization: authorization, //will be removed after discovery API implementation
        },
      },
    );

    return response.data;
  } catch (error: unknown) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error("Failed to download PDF blob", err);
    throw new Error("Unable to download PDF file.");
  }
}
