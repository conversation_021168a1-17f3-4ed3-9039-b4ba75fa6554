import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import Backend from "i18next-http-backend";
import LanguageDetector from "i18next-browser-languagedetector";

// Initialize i18n
i18n
  .use(Backend) // Load translations via HTTP (from public/locales/)
  .use(LanguageDetector) // Detect user language from browser
  .use(initReactI18next) // Connect with React
  .init({
    fallbackLng: "en", // Default if language can't be detected or loaded
    lng: "en", // Force English at startup (can be changed later)
    debug: import.meta.env.DEV, // Enable debug in development only

    interpolation: {
      escapeValue: false, // React already escapes by default
    },

    backend: {
      loadPath: "/locales/{{lng}}/{{ns}}.json", // Path to your translation files
    },

    ns: ["translation", "dashboard", "auth", "settings"],
    defaultNS: "translation", // shared/common

    react: {
      useSuspense: false, // Optional: disable suspense if not using lazy loading
    },
  });

export default i18n;
