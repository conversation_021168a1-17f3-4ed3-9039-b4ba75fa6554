import {
  DateRangePicker,
  type DateRangePickerChangeEvent,
} from "@progress/kendo-react-dateinputs";
import { Button } from "@progress/kendo-react-buttons";
import { filterClearIcon } from "@progress/kendo-svg-icons";
import "./DateRangeSelector.scss";

const DateRangeSelector = (props: any) => {
  const { value = null, onChange } = props;

  const handleChange = (event: DateRangePickerChangeEvent) => {
    const start = event.value?.start;
    const end = event.value?.end;

    onChange({
      value: {
        start: start ? start.toISOString() : null,
        end: end ? end.toISOString() : null,
      },
      operator: "inRange",
      syntheticEvent: event.syntheticEvent,
    });
  };

  const handleClear = (event: any) => {
    event.preventDefault();
    onChange({
      value: null,
      operator: "",
      syntheticEvent: event,
    });
  };

  const startDate = value?.start ? new Date(value.start) : null;
  const endDate = value?.end ? new Date(value.end) : null;

  return (
    <div className="gridCellFilterContainer">
      <DateRangePicker
        format="dd/MM/yy"
        value={{
          start: startDate,
          end: endDate,
        }}
        onChange={handleChange}
        clearButton
        startDateInputSettings={{
          placeholder: "From",
          label: "",
        }}
        endDateInputSettings={{
          placeholder: "To",
          label: "",
        }}
      />
      <Button
        svgIcon={filterClearIcon}
        title="Clear"
        type="button"
        onClick={handleClear}
        disabled={!value}
      />
    </div>
  );
};

export default DateRangeSelector;
