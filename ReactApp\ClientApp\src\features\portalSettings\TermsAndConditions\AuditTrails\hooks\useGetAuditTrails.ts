import { useGetAuditTrailsQuery } from "@/api/auditTrailsApiSlice";
import type {
  CompositeFilterDescriptor,
  SortDescriptor,
} from "@progress/kendo-data-query";
import { useMemo } from "react";

interface UseAuditTrailsProps {
  skip: number;
  take: number;
  filters: CompositeFilterDescriptor;
  sorts: SortDescriptor[];
}

export const useAuditTrails = ({
  skip,
  take,
  filters,
  sorts,
}: UseAuditTrailsProps) => {
  const { data, isLoading, isError, error } = useGetAuditTrailsQuery({
    skip,
    take,
    filters,
    sorts,
  });

  const auditTrails = useMemo(() => data?.records || [], [data]);
  const totalRecordCount = useMemo(() => data?.totalRecordCount || 0, [data]);

  return {
    auditTrails,
    totalRecordCount,
    isLoading,
    isError,
    error,
  };
};
