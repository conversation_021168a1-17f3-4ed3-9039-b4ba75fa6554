import { useState } from "react";
import logger from "@/utils/logger";

type SamplePayload = {
  id?: number;
  name: string;
  description: string;
};

export function useSampleFeatureController() {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");

  const [isSaving, setIsSaving] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  const handleChange = (field: "name" | "description", value: string) => {
    if (field === "name") setName(value);
    if (field === "description") setDescription(value);
  };

  const handleSave = async () => {
    const payload: SamplePayload = { name, description };

    try {
      setIsSaving(true);
      setErrorMessage("");
      setSuccessMessage("");

      logger.info("Saving sample...", payload);
      await new Promise((res) => setTimeout(res, 1000));

      setSuccessMessage("Sample saved successfully.");
    } catch (err: unknown) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error("Failed to save sample", {
        message: error.message,
        stack: error.stack,
      });
      setErrorMessage("Failed to save sample.");
    } finally {
      setIsSaving(false);
    }
  };

  return {
    name,
    description,
    isSaving,
    errorMessage,
    successMessage,
    handleChange,
    handleSave,
    onCloseError: () => setErrorMessage(""),
    onCloseSuccess: () => setSuccessMessage(""),
  };
}
