// components/common/AppliedFiltersDisplay/AppliedFiltersDisplay.tsx
import { ChipList } from "@progress/kendo-react-buttons";
import type {
  CompositeFilterDescriptor,
  FilterDescriptor,
} from "@progress/kendo-data-query";
import "./BaseGridAppliedFilters.scss";

interface BaseGridAppliedFiltersProps {
  filters: CompositeFilterDescriptor;
}

export default function BaseGridAppliedFilters({
  filters,
}: BaseGridAppliedFiltersProps) {
  const extractFilters = (
    filters: CompositeFilterDescriptor,
  ): FilterDescriptor[] => {
    const result: FilterDescriptor[] = [];

    filters?.filters?.forEach((f: any) => {
      if (f.filters) {
        result.push(...extractFilters(f));
      } else {
        result.push(f);
      }
    });

    return result;
  };

  const activeFilters = extractFilters(filters);

  if (activeFilters?.length === 0) return null;

  const renderValue = (val: any): string => {
    if (Array.isArray(val)) {
      return val
        .map((v) => (typeof v === "object" ? JSON.stringify(v) : String(v)))
        .join(", ");
    } else if (typeof val === "object" && val !== null) {
      return JSON.stringify(val);
    } else {
      return String(val);
    }
  };

  return (
    <div className="applied-filters">
      <strong>Filter By:</strong>
      <ChipList
        data={activeFilters?.map((filter) => ({
          text: `${filter?.field?.toString().toLocaleUpperCase()}: ${renderValue(filter.value)}`,
          removable: true,
        }))}
      />
    </div>
  );
}
