import type {
  SortDescriptor,
  CompositeFilterDescriptor,
} from "@progress/kendo-data-query";

export interface AuditTrail {
  id: number;
  fileName: string;
  fileSize?: number;
  uploadedBy?: string;
  uploadedOn?: string;
  acceptanceHistory: string;
}

export interface AuditTrailsColumn {
  key: string;
  displayValue: string;
  dataType: string;
  defaultVisible: boolean;
  sortable: boolean;
  filterType: string;
  filterData?: string;
}

export interface AuditTrailsResponse {
  records: AuditTrail[];
  pageCount: number;
  pageNumber: number;
  pageSize: number;
  totalRecordCount: number;
}

export interface GetAuditTrailsParams {
  skip: number;
  take: number;
  filters: CompositeFilterDescriptor;
  sorts: SortDescriptor[];
}
