import { Input } from "@progress/kendo-react-inputs";
import { SvgIcon } from "@progress/kendo-react-common";
import { searchIcon } from "@progress/kendo-svg-icons";

interface SidebarSearchProps {
  searchTerm: string;
  onSearchChange: (_value: string) => void;
}

export default function SidebarSearch({
  searchTerm,
  onSearchChange,
}: SidebarSearchProps) {
  return (
    <div className="search-wrapper">
      <Input
        value={searchTerm}
        onChange={(e) => onSearchChange(e.value)}
        placeholder="Search"
        className="search-input"
      />
      <SvgIcon icon={searchIcon} className="search-icon" />
    </div>
  );
}
