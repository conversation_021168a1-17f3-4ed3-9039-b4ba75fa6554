import { fetchBaseQuery } from "@reduxjs/toolkit/query";
import type {
  BaseQueryFn,
  FetchArgs,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query";
import type { RootState } from "@/store/store";
import config from "@/config";

const rawBaseQuery = fetchBaseQuery({
  baseUrl: config.baseUrl,
  prepareHeaders: (headers, { getState }) => {
    const state = getState() as RootState;
    const accessToken = state.okta.accessToken;

    if (accessToken) {
      headers.set("Authorization", `Bearer ${accessToken}`);
    }

    headers.set("Content-Type", "application/json");
    return headers;
  },
});

export const baseQueryWithReauth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const state = api.getState() as RootState;
  const tenantCode = state.okta.userInfo?.tenantCode?.[0];

  // Inject tenantCode into the URL
  if (typeof args === "string") {
    args = `${tenantCode}/${args}`;
  } else if (args?.url) {
    args.url = `${tenantCode}/${args.url}`;
  }

  let result = await rawBaseQuery(args, api, extraOptions);

  if (result.error && result.error.status === 401) {
    const refreshResult = await rawBaseQuery(
      "/refreshToken",
      api,
      extraOptions,
    );

    if (refreshResult.data) {
      // Optionally update token here
      result = await rawBaseQuery(args, api, extraOptions);
    } else {
      // api.dispatch(clearOktaAuth())
    }
  }

  return result;
};
