import React, { useState, useMemo } from "react";
import { TreeView, type ItemRenderProps } from "@progress/kendo-react-treeview";
import { Button } from "@progress/kendo-react-buttons";
import { Input } from "@progress/kendo-react-inputs";
import { SvgIcon } from "@progress/kendo-react-common";
import {
  folderIcon,
  plusIcon,
  pencilIcon,
  trashIcon,
  cancelIcon,
  exclamationCircleIcon,
} from "@progress/kendo-svg-icons";
import type {
  PrimaryFolder,
  TreeValidation,
} from "../hooks/useCreateTemplatePopup";

interface TreeViewDataItem {
  text: string;
  id: string;
  expanded?: boolean;
  hasChildren?: boolean;
  items?: TreeViewDataItem[];
  isPrimary?: boolean;
  isSecondary?: boolean;
  isEditing?: boolean;
  primaryFolderId?: string;
  secondaryCount?: number;
}

interface CreateTemplateTreeViewProps {
  primaryFolders: PrimaryFolder[];
  validation: TreeValidation;
  onAddPrimaryFolder: () => void;
  onAddSecondaryFolder: (_primaryFolderId: string) => void;
  onEditFolder: (
    _folderId: string,
    _isSecondary: boolean,
    _primaryFolderId?: string,
  ) => void;
  onSaveFolder: (
    _folderId: string,
    _newName: string,
    _isSecondary: boolean,
    _primaryFolderId?: string,
  ) => boolean;
  onCancelEdit: (
    _folderId: string,
    _isSecondary: boolean,
    _primaryFolderId?: string,
  ) => void;
  onDeleteFolder: (
    _folderId: string,
    _isSecondary: boolean,
    _primaryFolderId?: string,
  ) => void;
  onToggleExpand: (_primaryFolderId: string) => void;
}

export default function CreateTemplateTreeView({
  primaryFolders,
  validation,
  onAddPrimaryFolder,
  onAddSecondaryFolder,
  onEditFolder,
  onSaveFolder,
  onCancelEdit,
  onDeleteFolder,
  onToggleExpand,
}: CreateTemplateTreeViewProps) {
  const [editingValues, setEditingValues] = useState<Record<string, string>>(
    {},
  );

  const extractFolderNameAndCount = (
    text: string,
  ): { folderName: string; count: string } | null => {
    const match = text.match(/^(.+)\s\((\d+)\)$/);
    return match ? { folderName: match[1], count: match[2] } : null;
  };

  const treeData = useMemo(() => {
    return primaryFolders.map((primaryFolder) => ({
      text: `${primaryFolder.name} (${primaryFolder.secondaryFolders.length})`,
      id: primaryFolder.id,
      expanded: primaryFolder.expanded ?? true,
      hasChildren: true,
      isPrimary: true,
      isEditing: primaryFolder.isEditing,
      secondaryCount: primaryFolder.secondaryFolders.length,
      items: primaryFolder.secondaryFolders.map((secondaryFolder) => ({
        text: secondaryFolder.name,
        id: secondaryFolder.id,
        hasChildren: false,
        isSecondary: true,
        isEditing: secondaryFolder.isEditing,
        primaryFolderId: primaryFolder.id,
      })),
    }));
  }, [primaryFolders]);

  const handleEditValueChange = (folderId: string, value: string) => {
    setEditingValues((prev) => ({
      ...prev,
      [folderId]: value,
    }));
  };

  const handleSave = (
    folderId: string,
    isSecondary: boolean,
    primaryFolderId?: string,
  ) => {
    const newName = editingValues[folderId] || "";
    const success = onSaveFolder(
      folderId,
      newName,
      isSecondary,
      primaryFolderId,
    );

    if (success) {
      setEditingValues((prev) => {
        const newValues = { ...prev };
        delete newValues[folderId];
        return newValues;
      });
    }
  };

  const handleCancel = (
    folderId: string,
    isSecondary: boolean,
    primaryFolderId?: string,
  ) => {
    onCancelEdit(folderId, isSecondary, primaryFolderId);
    setEditingValues((prev) => {
      const newValues = { ...prev };
      delete newValues[folderId];
      return newValues;
    });
  };

  const handleInputKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    folderId: string,
    isSecondary: boolean,
    primaryFolderId?: string,
  ) => {
    e.stopPropagation();

    if (e.key === "Enter") {
      e.preventDefault();
      handleSave(folderId, isSecondary, primaryFolderId);
    }

    if (e.key === "Escape") {
      e.preventDefault();
      handleCancel(folderId, isSecondary, primaryFolderId);
    }
  };

  const itemRender = (itemProps: ItemRenderProps) => {
    const item = itemProps.item;
    const isEditing = item.isEditing;
    const isPrimary = item.isPrimary || false;
    const isSecondary = item.isSecondary || false;
    const canDelete = isPrimary ? primaryFolders.length > 1 : true;

    let editValue: string;
    if (editingValues[item.id] !== undefined) {
      editValue = editingValues[item.id];
    } else if (isPrimary) {
      const extracted = extractFolderNameAndCount(item.text);
      editValue = extracted ? extracted.folderName : item.text;
    } else {
      editValue = item.text;
    }

    let displayText: string | React.JSX.Element;
    if (isPrimary) {
      const extracted = extractFolderNameAndCount(item.text);
      if (extracted) {
        const { folderName, count } = extracted;
        displayText = (
          <>
            <span className="folder-name-text">{folderName}</span>{" "}
            <span style={{ color: "#0078d4" }}>({count})</span>
          </>
        );
      } else {
        displayText = item.text;
      }
    } else {
      displayText = item.text;
    }

    return (
      <div
        className={`tree-item ${isPrimary ? "primary-folder" : "secondary-folder"}`}
      >
        <div className="folder-content">
          <SvgIcon icon={folderIcon} className="folder-icon" />
          {isEditing ? (
            <div className="edit-container">
              <Input
                value={editValue}
                onChange={(e) => handleEditValueChange(item.id, e.value)}
                onKeyDown={(e) =>
                  handleInputKeyDown(
                    e,
                    item.id,
                    isSecondary,
                    item.primaryFolderId,
                  )
                }
                onBlur={() =>
                  handleSave(item.id, isSecondary, item.primaryFolderId)
                }
                className="edit-input"
                autoFocus
              />
              <div className="edit-actions">
                <Button
                  fillMode="flat"
                  size="small"
                  onClick={() =>
                    handleCancel(item.id, isSecondary, item.primaryFolderId)
                  }
                >
                  <SvgIcon icon={cancelIcon} />
                </Button>
              </div>
            </div>
          ) : (
            <>
              <span className="folder-name">{displayText}</span>
              <div className="folder-actions">
                {isPrimary && (
                  <Button
                    fillMode="flat"
                    size="small"
                    title="Add Secondary Folder"
                    onClick={() => onAddSecondaryFolder(item.id)}
                  >
                    <SvgIcon icon={plusIcon} />
                  </Button>
                )}
                <Button
                  fillMode="flat"
                  size="small"
                  onClick={() => {
                    let initialEditValue: string;
                    if (isPrimary) {
                      const extracted = extractFolderNameAndCount(item.text);
                      initialEditValue = extracted
                        ? extracted.folderName
                        : item.text;
                    } else {
                      initialEditValue = item.text;
                    }

                    setEditingValues((prev) => ({
                      ...prev,
                      [item.id]: initialEditValue,
                    }));
                    onEditFolder(item.id, isSecondary, item.primaryFolderId);
                  }}
                >
                  <SvgIcon icon={pencilIcon} />
                </Button>
                {canDelete && (
                  <Button
                    fillMode="flat"
                    size="small"
                    onClick={() =>
                      onDeleteFolder(item.id, isSecondary, item.primaryFolderId)
                    }
                  >
                    <SvgIcon icon={trashIcon} />
                  </Button>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="create-template-tree-view">
      <div className="tree-header">
        <h3>Folder Structure</h3>
        <Button
          size="small"
          icon="add"
          onClick={onAddPrimaryFolder}
          className="add-primary-folder-btn"
        >
          Add Primary Folder
        </Button>
      </div>

      {validation.duplicateNameError && (
        <div className="validation-error">
          <SvgIcon
            icon={exclamationCircleIcon}
            className="validation-error-icon"
          />
          <span>{validation.duplicateNameError}</span>
        </div>
      )}

      {validation.deleteLastSecondaryError && (
        <div className="validation-error">
          <SvgIcon
            icon={exclamationCircleIcon}
            className="validation-error-icon"
          />
          <span>{validation.deleteLastSecondaryError}</span>
        </div>
      )}

      <div className="tree-container">
        <TreeView
          data={treeData}
          expandIcons={true}
          expandField="expanded"
          hasChildrenField="hasChildren"
          item={itemRender}
          onExpandChange={(event) => {
            const item = event.item as TreeViewDataItem;
            if (item.isPrimary) {
              onToggleExpand(item.id);
            }
          }}
        />
      </div>
    </div>
  );
}
