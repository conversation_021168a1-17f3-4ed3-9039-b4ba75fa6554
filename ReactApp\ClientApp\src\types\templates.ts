export interface Template {
  id: number;
  templateName: string;
  createdBy: string;
  createdOn: string;
  description: string;
  status: string;
  assignedClients: string[];
}

export interface TemplateListResponse {
  records: Template[];
  pageCount: number;
  pageNumber: number;
  pageSize: number;
  totalRecordCount: number;
}

export interface TemplateNode {
  id: number;
  name: string;
  totalChildNodes: number;
  childNodes: TemplateChildNode[];
}

export interface TemplateChildNode {
  id: number;
  name: string;
}

export interface TemplateNodesResponse {
  nodes: TemplateNode[];
}
