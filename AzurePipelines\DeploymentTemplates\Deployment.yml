parameters:
  - name: targetContainer
    type: string
  - name: targetContainerTag
    type: string
  - name: serviceName
    type: string
  - name: serviceContainerType
    type: string
    default: "ClusterIP"
    values:
      - "LoadBalancer"
      - "ClusterIP"
      - "NodePort"
  - name: numberOfContainerInstances
    type: number
  - name: memoryLimit
    type: string
    default: "512Mi"
  - name: cpuLimit
    type: string
    default: "0.5"
  - name: hpaMinReplicas
    type: number
    default: 1
  - name: hpaMaxReplicas
    type: number
    default: 1
  - name: hpaMemoryAverageUtilization
    type: number
    default: 85
  - name: hpaCpuAverageUtilization
    type: number
    default: 75
  - name: advertisedAddress
    type: string
  - name: azureConfigurationLabel
    type: string
  - name: azureConfigurationFilter
    type: string
  - name: aksDeploymentOs
    type: string
  - name: aksDeploymentNamespace
    type: string
  - name: aksIngressClass
    type: string
  - name: aksIngressTlsIssuer
    type: string
  - name: containerPullAddress
    type: string
  - name: aksTemplateLocation
    type: string
  - name: aksTemplateName
    type: string
    default: "Deployment.template"
  - name: ingressWildCardDomain
    type: string
    default: ""
  - name: configMapName
    type: string
  - name: cpuRequests
    type: string
  - name: memoryRequests
    type: string
  - name: livenessProbeInitialDelaySeconds
    type: string
  - name: readinessProbeInitialDelaySeconds
    type: string
  - name: configMapMountPath
    type: string
  - name: configMapMountSubPath
    type: string

steps:
  - task: CopyFiles@2
    displayName: "Copy Deployment Template"
    inputs:
      SourceFolder: ${{ parameters.aksTemplateLocation }}
      Contents: ${{ parameters.aksTemplateName }}
      TargetFolder: "$(Pipeline.Workspace)/deployscripts"
      OverWrite: true
  - task: qetza.replacetokens.replacetokens-task.replacetokens@5
    displayName: "Replace tokens in ${{ parameters.aksTemplateName }}"
    inputs:
      rootDirectory: "$(Pipeline.Workspace)/deployscripts"
      targetFiles: ${{ parameters.aksTemplateName }}
      verbosity: detailed
      tokenPattern: "doublebraces"
      actionOnMissing: fail
      actionOnNoFiles: fail
      ${{ if ne(parameters.ingressWildCardDomain, '')}}:
        inlineVariables: |
          acrpullendpoint: ${{ parameters.containerPullAddress }}
          containerimage: ${{ parameters.targetContainer }}
          servicename: ${{ parameters.serviceName }}
          replicas: ${{ parameters.numberOfContainerInstances }}
          memorylimits: ${{ parameters.memoryLimit }}
          cpuLimits: ${{ parameters.cpuLimit }}
          fqdn: ${{ parameters.advertisedAddress }}
          azureconfiglabel: ${{ parameters.azureConfigurationLabel }}
          azureconfigfilters: '${{ parameters.azureConfigurationFilter }}'
          akshostos: ${{ parameters.aksDeploymentOs }}
          ingressclass: ${{ parameters.aksIngressClass }}
          tlsissuer: ${{ parameters.aksIngressTlsIssuer }}
          aksnamespace: ${{ parameters.aksDeploymentNamespace }}
          containerServiceType: ${{ parameters.serviceContainerType }}
          configMapName: ${{ parameters.configMapName }}
          wildCardDomain: ${{ parameters.ingressWildCardDomain }}
          cpurequests: ${{ parameters.cpuRequests }}
          memoryrequests: ${{ parameters.memoryRequests }}
          livenessProbeInitialDelaySeconds: ${{ parameters.livenessProbeInitialDelaySeconds }}
          readinessProbeInitialDelaySeconds: ${{ parameters.readinessProbeInitialDelaySeconds }}
          configMapMountPath: ${{ parameters.configMapMountPath }}
          configMapMountSubPath: ${{ parameters.configMapMountSubPath }}
          hpaMinReplicas: ${{ parameters.hpaMinReplicas }}
          hpaMaxReplicas: ${{ parameters.hpaMaxReplicas }}
          hpaMemoryAverageUtilization: ${{ parameters.hpaMemoryAverageUtilization }}
          hpaCpuAverageUtilization: ${{ parameters.hpaCpuAverageUtilization }}
      ${{ else }}:
        inlineVariables: |
          acrpullendpoint: ${{ parameters.containerPullAddress }}
          containerimage: ${{ parameters.targetContainer }}
          servicename: ${{ parameters.serviceName }}
          replicas: ${{ parameters.numberOfContainerInstances }}
          memorylimits: ${{ parameters.memoryLimit }}
          cpuLimits: ${{ parameters.cpuLimit }}
          fqdn: ${{ parameters.advertisedAddress }}
          azureconfiglabel: ${{ parameters.azureConfigurationLabel }}
          azureconfigfilters: '${{ parameters.azureConfigurationFilter }}'
          akshostos: ${{ parameters.aksDeploymentOs }}
          ingressclass: ${{ parameters.aksIngressClass }}
          tlsissuer: ${{ parameters.aksIngressTlsIssuer }}
          aksnamespace: ${{ parameters.aksDeploymentNamespace }}
          containerServiceType: ${{ parameters.serviceContainerType }}
          configMapName: ${{ parameters.configMapName }}
          cpurequests: ${{ parameters.cpuRequests }}
          memoryrequests: ${{ parameters.memoryRequests }}
          livenessProbeInitialDelaySeconds: ${{ parameters.livenessProbeInitialDelaySeconds }}
          readinessProbeInitialDelaySeconds: ${{ parameters.readinessProbeInitialDelaySeconds }}
          configMapMountPath: ${{ parameters.configMapMountPath }}
          configMapMountSubPath: ${{ parameters.configMapMountSubPath }}
          hpaMinReplicas: ${{ parameters.hpaMinReplicas }}
          hpaMaxReplicas: ${{ parameters.hpaMaxReplicas }}
          hpaMemoryAverageUtilization: ${{ parameters.hpaMemoryAverageUtilization }}
          hpaCpuAverageUtilization: ${{ parameters.hpaCpuAverageUtilization }}

      enableTelemetry: false
  - task: KubernetesManifest@0
    inputs:
      action: "createSecret"
      namespace: ${{ parameters.aksDeploymentNamespace }}
      secretType: "dockerRegistry"
      secretName: $(AzureContainerRegistryImagePullSecret)
      dockerRegistryEndpoint: $(AzureContainerRegistry)
  - task: KubernetesManifest@0
    continueOnError: true
    inputs:
      action: "deploy"
      namespace: ${{ parameters.aksDeploymentNamespace }}
      manifests: "$(Pipeline.Workspace)/deployscripts/${{ parameters.aksTemplateName }}"
      containers: "${{ parameters.containerPullAddress }}/${{ parameters.targetContainer }}:${{ parameters.targetContainerTag }}"
      imagePullSecrets: $(AzureContainerRegistryImagePullSecret)
      rolloutStatusTimeout: "300"
