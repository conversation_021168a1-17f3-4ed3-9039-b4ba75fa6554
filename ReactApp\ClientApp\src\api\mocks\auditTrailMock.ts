export const mockData = {
  records: [
    {
      id: 1,
      fileName: "Report_Q1_2024.pdf",
      fileSize: "15",
      uploadedBy: "<PERSON>",
      uploadedOn: "2025-06-01T09:00:00.000Z",
      acceptanceHistory: "/api/files/1/acceptance-history",
    },
    {
      id: 2,
      fileName: "Project_Plan_V2.docx",
      fileSize: "5",
      uploadedBy: "<PERSON>",
      uploadedOn: "2025-06-02T09:00:00.000Z",
      acceptanceHistory: "/api/files/2/status-history",
    },
    {
      id: 3,
      fileName: "Design_Specs_Alpha.zip",
      fileSize: "45",
      uploadedBy: "<PERSON>",
      uploadedOn: "2025-06-03T09:00:00.000Z",
      acceptanceHistory: "/api/files/3/acceptance-history",
    },
    {
      id: 4,
      fileName: "Marketing_Brief_Final.pptx",
      fileSize: "10",
      uploadedBy: "<PERSON>",
      uploadedOn: "2025-06-04T09:00:00.000Z",
      acceptanceHistory: "/api/files/4/acceptance-history",
    },
    {
      id: 5,
      fileName: "Budget_Summary_April.xlsx",
      fileSize: "2",
      uploadedBy: "<PERSON>",
      uploadedOn: "2025-06-05T09:00:00.000Z",
      acceptanceHistory: "/api/files/5/acceptance-history",
    },
    {
      id: 6,
      fileName: "Client_Agreement_XYZ.pdf",
      fileSize: "8",
      uploadedBy: "Frank White",
      uploadedOn: "2025-06-06T09:00:00.000Z",
      acceptanceHistory: "/api/files/6/acceptance-history",
    },
    {
      id: 7,
      fileName: "Meeting_Minutes_0408.docx",
      fileSize: "1",
      uploadedBy: "Grace Hall",
      uploadedOn: "2025-06-07T09:00:00.000Z",
      acceptanceHistory: "/api/files/7/status-history",
    },
    {
      id: 8,
      fileName: "Code_Review_Report.pdf",
      fileSize: "3",
      uploadedBy: "Harry Green",
      uploadedOn: "2025-06-08T09:00:00.000Z",
      acceptanceHistory: "/api/files/8/acceptance-history",
    },
    {
      id: 9,
      fileName: "User_Feedback_Q1.csv",
      fileSize: "7",
      uploadedBy: "Ivy King",
      uploadedOn: "2025-06-09T09:00:00.000Z",
      acceptanceHistory: "/api/files/9/history",
    },
    {
      id: 10,
      fileName: "Training_Manual_V1.pdf",
      fileSize: "25",
      uploadedBy: "Jack Scott",
      uploadedOn: "2025-06-10T09:00:00.000Z",
      acceptanceHistory: "/api/files/10/acceptance-history",
    },
    {
      id: 11,
      fileName: "Sales_Forecast_May.xlsx",
      fileSize: "4",
      uploadedBy: "Karen Bell",
      uploadedOn: "2025-06-11T09:00:00.000Z",
      acceptanceHistory: "/api/files/11/acceptance-history",
    },
    {
      id: 12,
      fileName: "HR_Policy_Update.pdf",
      fileSize: "12",
      uploadedBy: "Liam Young",
      uploadedOn: "2025-06-12T09:00:00.000Z",
      acceptanceHistory: "/api/files/12/acceptance-history",
    },
    {
      id: 13,
      fileName: "Dev_Roadmap_Q3.pptx",
      fileSize: "9",
      uploadedBy: "Mia Clark",
      uploadedOn: "2025-06-13T09:00:00.000Z",
      acceptanceHistory: "/api/files/13/history",
    },
    {
      id: 14,
      fileName: "Customer_Survey_Results.pdf",
      fileSize: "18",
      uploadedBy: "Noah Lewis",
      uploadedOn: "2025-06-14T09:00:00.000Z",
      acceptanceHistory: "/api/files/14/history",
    },
    {
      id: 15,
      fileName: "Backend_API_Docs.json",
      fileSize: "6",
      uploadedBy: "Olivia Harris",
      uploadedOn: "2025-06-15T09:00:00.000Z",
      acceptanceHistory: "/api/files/15/status-history",
    },
    {
      id: 16,
      fileName: "Frontend_Component_Library.zip",
      fileSize: "30",
      uploadedBy: "Peter Martin",
      uploadedOn: "2025-06-16T09:00:00.000Z",
      acceptanceHistory: "/api/files/16/acceptance-history",
    },
    {
      id: 17,
      fileName: "Compliance_Checklist.docx",
      fileSize: "3",
      uploadedBy: "Quinn Nelson",
      uploadedOn: "2025-06-17T09:00:00.000Z",
      acceptanceHistory: "/api/files/17/acceptance-history",
    },
    {
      id: 18,
      fileName: "Internal_Audit_Report.pdf",
      fileSize: "22",
      uploadedBy: "Rachel Baker",
      uploadedOn: "2025-06-18T09:00:00.000Z",
      acceptanceHistory: "/api/files/18/history",
    },
    {
      id: 19,
      fileName: "Website_Content_Plan.xlsx",
      fileSize: "1",
      uploadedBy: "Sam Turner",
      uploadedOn: "2025-06-19T09:00:00.000Z",
      acceptanceHistory: "/api/files/19/acceptance-history",
    },
    {
      id: 20,
      fileName: "Legal_Document_A.pdf",
      fileSize: "10",
      uploadedBy: "Tina Roberts",
      uploadedOn: "2025-06-20T09:00:00.000Z",
      acceptanceHistory: "/api/files/20/acceptance-history",
    },
    {
      id: 21,
      fileName: "Onboarding_Guide.pdf",
      fileSize: "18",
      uploadedBy: "Uma White",
      uploadedOn: "2025-06-21T09:00:00.000Z",
      acceptanceHistory: "/api/files/21/acceptance-history",
    },
    {
      id: 22,
      fileName: "Server_Logs_May.zip",
      fileSize: "50",
      uploadedBy: "Victor Adams",
      uploadedOn: "2025-06-22T09:00:00.000Z",
      acceptanceHistory: "/api/files/22/acceptance-history",
    },
    {
      id: 23,
      fileName: "Invoice_Template.docx",
      fileSize: "0.5",
      uploadedBy: "Wendy Harris",
      uploadedOn: "2025-06-23T09:00:00.000Z",
      acceptanceHistory: "/api/files/23/acceptance-history",
    },
    {
      id: 24,
      fileName: "Sprint_Review_Q2.pptx",
      fileSize: "7",
      uploadedBy: "Xavier Davis",
      uploadedOn: "2025-06-24T09:00:00.000Z",
      acceptanceHistory: "/api/files/24/history",
    },
    {
      id: 25,
      fileName: "Client_Feedback_Survey.pdf",
      fileSize: "4",
      uploadedBy: "Yara Lopez",
      uploadedOn: "2025-06-25T09:00:00.000Z",
      acceptanceHistory: "/api/files/25/status-history",
    },
  ],

  pageCount: 2,
  pageNuer: 1,
  pageSize: 20,
  totalRecordCount: 32,
};

export const mockColumns = [
  {
    key: "fileName",
    displayValue: "File Name",
    dataType: "text",
    defaultVisible: true,
    sortable: true,
    filterType: "search",
  },
  {
    key: "fileSize",
    displayValue: "File Size",
    dataType: "text",
    defaultVisible: true,
    sortable: true,
    filterType: "range",
  },
  {
    key: "uploadedBy",
    displayValue: "Uploaded By",
    dataType: "text",
    defaultVisible: true,
    sortable: true,
    filterType: "search",
  },
  {
    key: "uploadedOn",
    displayValue: "Uploaded On",
    dataType: "date",
    defaultVisible: true,
    sortable: true,
    filterType: "range",
  },
  {
    key: "acceptanceHistory",
    displayValue: "Acceptance History",
    dataType: "text",
    defaultVisible: false,
    sortable: true,
    filterType: "none",
  },
];
