FROM mcr.microsoft.com/dotnet/aspnet:8.0-jammy AS base
WORKDIR /app
EXPOSE 80
RUN    apt-get update -yq \
    && apt-get upgrade -yq \
    && apt-get install -yq curl \
    && curl -sL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

FROM mcr.microsoft.com/dotnet/sdk:8.0-jammy AS build
RUN    apt-get update -yq \
    && apt-get upgrade -yq \
    && apt-get install -yq curl \
    && curl -sL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

ENV DOTNET_SYSTEM_NET_HTTP_USESOCKETSHTTPHANDLER=0
ENV NUGET_CREDENTIALPROVIDER_SESSIONTOKENCACHE_ENABLED=true
    
WORKDIR /src
COPY ReactApp ./ReactApp
RUN rm -rf ReactApp/ClientApp/public/env-config.js
RUN dotnet restore "ReactApp/ReactApp.csproj"

FROM build AS publish
RUN dotnet publish "ReactApp/ReactApp.csproj" -c Release --no-restore -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENV ASPNETCORE_ENVIRONMENT="Production"
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENTRYPOINT ["dotnet", "ReactApp.dll"]
