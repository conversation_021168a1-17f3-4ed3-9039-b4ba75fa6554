import { useSaveSampleMutation } from "@/api/sampleApiSlice";
import logger from "@/utils/logger";

export const useSaveSampleFeature = () => {
  const [saveSample, { isLoading }] = useSaveSampleMutation();

  const save = async (
    payload: any,
    onError: (_message: string) => void,
    onSuccess?: () => void,
  ) => {
    try {
      await saveSample(payload).unwrap();
      onSuccess?.();
    } catch (error: any) {
      logger.error("Save error", error);
      onError(error?.data?.message || "Save failed.");
    }
  };

  return { save, isSaving: isLoading };
};
