import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "./interceptorsSlice";

// Define type for a single ToDo
export interface ToDo {
  id: number;
  isDone: boolean;
  title: string;
}

// Optional: Input type for creating/updating a ToDo
export interface ToDoInput {
  title: string;
  isDone?: boolean;
}

// RTK Query API slice
export const todoApiSlice = createApi({
  reducerPath: "todoApi",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["ToDo"],
  endpoints: (builder) => ({
    getToDos: builder.query<ToDo[], number | void>({
      query: (_limit = 10) => ({
        url: "/todos",
        params: { _limit },
      }),
      providesTags: ["ToDo"],
    }),

    getToDo: builder.query<ToDo, number>({
      query: (id) => ({
        url: `/todos/${id}`,
      }),
    }),

    createToDo: builder.mutation<ToDo, ToDoInput>({
      query: (data) => ({
        url: "/todos",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["ToDo"],
    }),

    updateToDo: builder.mutation<ToDo, ToDo>({
      query: ({ id, ...rest }) => ({
        url: `/todos/${id}`,
        method: "PUT",
        body: rest,
      }),
      invalidatesTags: ["ToDo"],
    }),

    deleteToDo: builder.mutation<void, number>({
      query: (id) => ({
        url: `/todos/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["ToDo"],
    }),
  }),
});

// Auto-generated hooks
export const {
  useLazyGetToDosQuery,
  useGetToDoQuery,
  useCreateToDoMutation,
  useUpdateToDoMutation,
  useDeleteToDoMutation,
} = todoApiSlice;
