const oktaConfig = {
  issuer: `${import.meta.env.VITE_OKTA_DOMAIN}/oauth2/default`,
  clientId: import.meta.env.VITE_OKTA_CLIENT_ID,
  redirectUri: window.location.origin + "/login/callback",
  postLogoutRedirectUri: window.location.origin + "/logout",
  scopes: ["openid", "profile", "tenancy"],
  tokenManager: {
    autoRenew: true,
    secure: true,
    storageKey: "AP_AUTH_TOKEN",
  },
};

export default oktaConfig;
