import { renderHook, act } from "@testing-library/react";
import { vi } from "vitest";
import { useLandingPageController } from "./useLandingPageController";

// Mock the useGetRecentActivity hook
const mockUseGetRecentActivity = vi.fn();
vi.mock("./useGetRecentActivity", () => ({
  useGetRecentActivity: () => mockUseGetRecentActivity(),
}));

describe("useLandingPageController", () => {
  beforeEach(() => {
    mockUseGetRecentActivity.mockClear();
  });

  it("returns initial state correctly", () => {
    const mockRefetch = vi.fn();
    mockUseGetRecentActivity.mockReturnValue({
      recentActivity: null,
      isFetching: false,
      isLoading: false,
      error: null,
      refetchRecentActivity: mockRefetch,
    });

    const { result } = renderHook(() => useLandingPageController());

    expect(result.current.showAlert).toBe(true);
    expect(result.current.recentActivityData).toEqual([]);
    expect(result.current.totalRecords).toBe(0);
    expect(result.current.dataState).toEqual({
      skip: 0,
      take: 20,
      sort: [{ field: "lastUpdated", dir: "desc" }]
    });
    expect(result.current.isLoadingRecentActivity).toBe(false);
    expect(result.current.isFetchingRecentActivity).toBe(false);
    expect(result.current.recentActivityError).toBe(null);
    expect(typeof result.current.setShowAlert).toBe("function");
    expect(typeof result.current.handleRefreshRecentActivity).toBe("function");
    expect(typeof result.current.handleDataStateChange).toBe("function");
  });

  it("returns recent activity data when response exists", () => {
    const mockData = [
      {
        id: "1",
        setupArea: "DMS Settings" as const,
        section: "Test Section",
        updatedBy: "Test User",
        lastUpdated: "2025-01-01",
      },
    ];

    mockUseGetRecentActivity.mockReturnValue({
      recentActivity: {
        records: mockData,
        pageCount: 1,
        pageNumber: 1,
        pageSize: 20,
        totalRecordCount: 1
      },
      isFetching: false,
      isLoading: false,
      error: null,
      refetchRecentActivity: vi.fn(),
    });

    const { result } = renderHook(() => useLandingPageController());

    expect(result.current.recentActivityData).toEqual(mockData);
    expect(result.current.totalRecords).toBe(1);
  });

  it("returns empty array when response is null", () => {
    mockUseGetRecentActivity.mockReturnValue({
      recentActivity: null,
      isFetching: false,
      isLoading: false,
      error: null,
      refetchRecentActivity: vi.fn(),
    });

    const { result } = renderHook(() => useLandingPageController());

    expect(result.current.recentActivityData).toEqual([]);
    expect(result.current.totalRecords).toBe(0);
  });

  it("returns empty array when response data is undefined", () => {
    mockUseGetRecentActivity.mockReturnValue({
      recentActivity: {
        records: undefined,
        pageCount: 0,
        pageNumber: 1,
        pageSize: 20,
        totalRecordCount: 0
      },
      isFetching: false,
      isLoading: false,
      error: null,
      refetchRecentActivity: vi.fn(),
    });

    const { result } = renderHook(() => useLandingPageController());

    expect(result.current.recentActivityData).toEqual([]);
    expect(result.current.totalRecords).toBe(0);
  });

  it("updates showAlert state when setShowAlert is called", () => {
    mockUseGetRecentActivity.mockReturnValue({
      recentActivity: null,
      isFetching: false,
      isLoading: false,
      error: null,
      refetchRecentActivity: vi.fn(),
    });

    const { result } = renderHook(() => useLandingPageController());

    expect(result.current.showAlert).toBe(true);

    act(() => {
      result.current.setShowAlert(false);
    });

    expect(result.current.showAlert).toBe(false);

    act(() => {
      result.current.setShowAlert(true);
    });

    expect(result.current.showAlert).toBe(true);
  });

  it("calls refetchRecentActivity when handleRefreshRecentActivity is called", () => {
    const mockRefetch = vi.fn();
    mockUseGetRecentActivity.mockReturnValue({
      recentActivity: null,
      isFetching: false,
      isLoading: false,
      error: null,
      refetchRecentActivity: mockRefetch,
    });

    const { result } = renderHook(() => useLandingPageController());

    act(() => {
      result.current.handleRefreshRecentActivity();
    });

    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });

  it("passes through loading states correctly", () => {
    mockUseGetRecentActivity.mockReturnValue({
      recentActivity: null,
      isFetching: true,
      isLoading: true,
      error: null,
      refetchRecentActivity: vi.fn(),
    });

    const { result } = renderHook(() => useLandingPageController());

    expect(result.current.isLoadingRecentActivity).toBe(true);
    expect(result.current.isFetchingRecentActivity).toBe(true);
  });

  it("passes through error state correctly", () => {
    const mockError = { message: "Test error" };
    mockUseGetRecentActivity.mockReturnValue({
      recentActivity: null,
      isFetching: false,
      isLoading: false,
      error: mockError,
      refetchRecentActivity: vi.fn(),
    });

    const { result } = renderHook(() => useLandingPageController());

    expect(result.current.recentActivityError).toBe(mockError);
  });

  it("maintains state consistency across re-renders", () => {
    const mockRefetch = vi.fn();
    mockUseGetRecentActivity.mockReturnValue({
      recentActivity: null,
      isFetching: false,
      isLoading: false,
      error: null,
      refetchRecentActivity: mockRefetch,
    });

    const { result, rerender } = renderHook(() => useLandingPageController());

    act(() => {
      result.current.setShowAlert(false);
    });

    expect(result.current.showAlert).toBe(false);

    rerender();

    expect(result.current.showAlert).toBe(false);
  });

  it("handles data state change correctly", () => {
    mockUseGetRecentActivity.mockReturnValue({
      recentActivity: null,
      isFetching: false,
      isLoading: false,
      error: null,
      refetchRecentActivity: vi.fn(),
    });

    const { result } = renderHook(() => useLandingPageController());

    const newDataState = {
      skip: 20,
      take: 50,
      sort: [{ field: "updatedBy", dir: "asc" }]
    };

    act(() => {
      result.current.handleDataStateChange({ dataState: newDataState });
    });

    expect(result.current.dataState).toEqual(newDataState);
  });
});
