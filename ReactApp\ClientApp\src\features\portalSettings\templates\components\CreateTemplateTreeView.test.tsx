import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { vi } from "vitest";
import CreateTemplateTreeView from "./CreateTemplateTreeView";
import type { PrimaryFolder, TreeValidation } from "../hooks/useCreateTemplatePopup";

describe("CreateTemplateTreeView", () => {
  const mockPrimaryFolders: PrimaryFolder[] = [
    {
      id: "primary-1",
      name: "Primary Folder",
      isEditing: false,
      expanded: true,
      secondaryFolders: [
        {
          id: "secondary-1",
          name: "Secondary Folder",
          isEditing: false,
        },
        {
          id: "secondary-2",
          name: "Another Secondary",
          isEditing: false,
        },
      ],
    },
  ];

  const mockValidation: TreeValidation = {
    duplicateNameError: "",
    deleteLastSecondaryError: "",
  };

  const mockProps = {
    primaryFolders: mockPrimaryFolders,
    validation: mockValidation,
    onAddPrimaryFolder: vi.fn(),
    onAddSecondaryFolder: vi.fn(),
    onEditFolder: vi.fn(),
    onSaveFolder: vi.fn().mockReturnValue(true),
    onCancelEdit: vi.fn(),
    onDeleteFolder: vi.fn(),
    onToggleExpand: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the tree view with folders", () => {
    render(<CreateTemplateTreeView {...mockProps} />);

    expect(screen.getByText("Folder Structure")).toBeInTheDocument();
    // Check for the button text
    expect(screen.getByText("Add Primary Folder")).toBeInTheDocument();
    // Check for the folder names
    expect(screen.getByText("Secondary Folder")).toBeInTheDocument();
    expect(screen.getByText("Another Secondary")).toBeInTheDocument();
    // Check that the primary folder with count is rendered (using a more specific selector)
    expect(screen.getByText("(2)")).toBeInTheDocument();
  });

  it("calls onEditFolder when edit button is clicked", async () => {
    const user = userEvent.setup();
    render(<CreateTemplateTreeView {...mockProps} />);

    // Find edit buttons by looking for pencil icon SVG
    const editButtons = screen.getAllByRole("button").filter(button => {
      const svg = button.querySelector('svg');
      return svg && svg.querySelector('path[d*="334.9 86.6"]'); // Part of pencil icon path
    });

    expect(editButtons.length).toBeGreaterThan(0);
    await user.click(editButtons[0]);
    expect(mockProps.onEditFolder).toHaveBeenCalled();
  });

  it("handles duplicate name validation correctly on blur", async () => {
    const user = userEvent.setup();

    // Mock folders with one in editing mode
    const editingFolders: PrimaryFolder[] = [
      {
        id: "primary-1",
        name: "Primary Folder",
        isEditing: false,
        expanded: true,
        secondaryFolders: [
          {
            id: "secondary-1",
            name: "Secondary Folder",
            isEditing: true, // This folder is being edited
          },
          {
            id: "secondary-2",
            name: "Another Secondary",
            isEditing: false,
          },
        ],
      },
    ];

    const propsWithEditing = {
      ...mockProps,
      primaryFolders: editingFolders,
    };

    render(<CreateTemplateTreeView {...propsWithEditing} />);

    // Find the input field for the editing folder
    const input = screen.getByDisplayValue("Secondary Folder");
    expect(input).toBeInTheDocument();

    // Type a duplicate name (this will update the editingValues state)
    await user.clear(input);
    await user.type(input, "Another Secondary");

    // Simulate blur event
    await user.click(document.body); // Click outside to trigger blur

    // Verify that onSaveFolder was called with the current input value
    expect(mockProps.onSaveFolder).toHaveBeenCalledWith(
      "secondary-1",
      "Another Secondary",
      true,
      "primary-1"
    );
  });

  it("handles empty name validation correctly on blur", async () => {
    const user = userEvent.setup();

    // Mock folders with one in editing mode
    const editingFolders: PrimaryFolder[] = [
      {
        id: "primary-1",
        name: "Primary Folder",
        isEditing: false,
        expanded: true,
        secondaryFolders: [
          {
            id: "secondary-1",
            name: "Secondary Folder",
            isEditing: true, // This folder is being edited
          },
        ],
      },
    ];

    const propsWithEditing = {
      ...mockProps,
      primaryFolders: editingFolders,
    };

    render(<CreateTemplateTreeView {...propsWithEditing} />);

    // Find the input field for the editing folder
    const input = screen.getByDisplayValue("Secondary Folder");
    expect(input).toBeInTheDocument();

    // Clear the input to make it empty
    await user.clear(input);

    // Simulate blur event
    await user.click(document.body); // Click outside to trigger blur

    // Verify that onSaveFolder was called with empty string
    expect(mockProps.onSaveFolder).toHaveBeenCalledWith(
      "secondary-1",
      "",
      true,
      "primary-1"
    );
  });

  it("shows validation error messages", () => {
    const propsWithError = {
      ...mockProps,
      validation: {
        duplicateNameError: "A folder with this name already exists in the same level",
        deleteLastSecondaryError: "",
      },
    };

    render(<CreateTemplateTreeView {...propsWithError} />);
    
    expect(screen.getByText("A folder with this name already exists in the same level")).toBeInTheDocument();
  });

  it("calls onSaveFolder with Enter key", async () => {
    const user = userEvent.setup();

    // Mock folders with one in editing mode
    const editingFolders: PrimaryFolder[] = [
      {
        id: "primary-1",
        name: "Primary Folder",
        isEditing: false,
        expanded: true,
        secondaryFolders: [
          {
            id: "secondary-1",
            name: "Secondary Folder",
            isEditing: true,
          },
        ],
      },
    ];

    const propsWithEditing = {
      ...mockProps,
      primaryFolders: editingFolders,
    };

    render(<CreateTemplateTreeView {...propsWithEditing} />);

    const input = screen.getByDisplayValue("Secondary Folder");
    await user.clear(input);
    await user.type(input, "New Name");
    await user.keyboard("{Enter}");

    expect(mockProps.onSaveFolder).toHaveBeenCalledWith(
      "secondary-1",
      "New Name",
      true,
      "primary-1"
    );
  });

  it("handles the specific bug scenario: duplicate name followed by blur with reverted input", async () => {
    const user = userEvent.setup();

    // Mock the saveFolder function to return false for duplicate names
    const mockSaveFolder = vi.fn().mockImplementation((_folderId, newName) => {
      // Return false for duplicate names, true for others
      return newName !== "Another Secondary";
    });

    // Mock folders with one in editing mode
    const editingFolders: PrimaryFolder[] = [
      {
        id: "primary-1",
        name: "Primary Folder",
        isEditing: false,
        expanded: true,
        secondaryFolders: [
          {
            id: "secondary-1",
            name: "Secondary Folder",
            isEditing: true,
          },
          {
            id: "secondary-2",
            name: "Another Secondary",
            isEditing: false,
          },
        ],
      },
    ];

    const propsWithEditing = {
      ...mockProps,
      primaryFolders: editingFolders,
      onSaveFolder: mockSaveFolder,
    };

    render(<CreateTemplateTreeView {...propsWithEditing} />);

    const input = screen.getByDisplayValue("Secondary Folder");

    // Step 1: User types a duplicate name
    await user.clear(input);
    await user.type(input, "Another Secondary");

    // Step 2: User presses Enter (or blur happens) - validation fails
    await user.keyboard("{Enter}");

    // Verify that saveFolder was called with the duplicate name and returned false
    expect(mockSaveFolder).toHaveBeenCalledWith(
      "secondary-1",
      "Another Secondary",
      true,
      "primary-1"
    );

    // Step 3: User clicks elsewhere (blur event) - this should not cause "empty name" error
    // Since the editingValues should have been cleared after the failed save
    await user.click(document.body);

    // The second call should be with empty string (since editingValues was cleared)
    // This verifies that the bug is fixed - we don't get stuck with stale duplicate data
    expect(mockSaveFolder).toHaveBeenLastCalledWith(
      "secondary-1",
      "",
      true,
      "primary-1"
    );
  });
});
