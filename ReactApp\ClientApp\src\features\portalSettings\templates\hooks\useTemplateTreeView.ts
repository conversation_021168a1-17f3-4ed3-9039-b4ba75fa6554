import { useState, useCallback } from "react";
import type {
  PrimaryFolder,
  SecondaryFolder,
  TreeValidation,
} from "./useCreateTemplatePopup";

export function useTemplateTreeView() {
  const generateUniqueId = useCallback(() => {
    const timestamp = Date.now();
    const randomPart = Math.random().toString(36).slice(2, 10);
    return `folder-${timestamp}-${randomPart}`;
  }, []);

  const [primaryFolders, setPrimaryFolders] = useState<PrimaryFolder[]>([
    {
      id: generateUniqueId(),
      name: "Primary Folder",
      isEditing: false,
      expanded: true,
      secondaryFolders: [
        {
          id: generateUniqueId(),
          name: "Secondary Folder",
          isEditing: false,
        },
      ],
    },
  ]);

  const [validation, setValidation] = useState<TreeValidation>({
    duplicateNameError: "",
    deleteLastSecondaryError: "",
  });

  const clearValidationErrors = useCallback(() => {
    setValidation({
      duplicateNameError: "",
      deleteLastSecondaryError: "",
    });
  }, []);

  const generateUniqueName = useCallback(
    (baseName: string, existingNames: string[]) => {
      let name = baseName;
      let counter = 1;

      while (existingNames.includes(name)) {
        name = `${baseName}(${counter})`;
        counter++;
      }

      return name;
    },
    [],
  );

  const getAllPrimaryFolderNames = useCallback(() => {
    return primaryFolders.map((folder) => folder.name);
  }, [primaryFolders]);

  const getAllSecondaryFolderNames = useCallback(
    (primaryFolderId: string) => {
      const primaryFolder = primaryFolders.find(
        (f) => f.id === primaryFolderId,
      );
      return primaryFolder
        ? primaryFolder.secondaryFolders.map((f) => f.name)
        : [];
    },
    [primaryFolders],
  );

  const validateFolderName = useCallback(
    (
      newName: string,
      folderId: string,
      isSecondary: boolean,
      primaryFolderId?: string,
    ): boolean => {
      clearValidationErrors();

      if (!newName.trim()) {
        setValidation((prev) => ({
          ...prev,
          duplicateNameError: "Folder name cannot be empty",
        }));
        return false;
      }

      if (isSecondary && primaryFolderId) {
        const secondaryNames = getAllSecondaryFolderNames(primaryFolderId);
        const otherNames = secondaryNames.filter((_, index) => {
          const primaryFolder = primaryFolders.find(
            (f) => f.id === primaryFolderId,
          );
          return primaryFolder?.secondaryFolders[index]?.id !== folderId;
        });

        if (otherNames.includes(newName.trim())) {
          setValidation((prev) => ({
            ...prev,
            duplicateNameError:
              "A folder with this name already exists in the same level",
          }));
          return false;
        }
      } else {
        const primaryNames = getAllPrimaryFolderNames();
        const otherNames = primaryNames.filter((_, index) => {
          return primaryFolders[index]?.id !== folderId;
        });

        if (otherNames.includes(newName.trim())) {
          setValidation((prev) => ({
            ...prev,
            duplicateNameError: "A folder with this name already exists",
          }));
          return false;
        }
      }

      return true;
    },
    [
      primaryFolders,
      getAllPrimaryFolderNames,
      getAllSecondaryFolderNames,
      clearValidationErrors,
    ],
  );

  const addPrimaryFolder = useCallback(() => {
    clearValidationErrors();
    const existingNames = getAllPrimaryFolderNames();
    const newName = generateUniqueName("Primary Folder", existingNames);

    const newFolder: PrimaryFolder = {
      id: generateUniqueId(),
      name: newName,
      isEditing: false,
      expanded: true,
      secondaryFolders: [
        {
          id: generateUniqueId(),
          name: "Secondary Folder",
          isEditing: false,
        },
      ],
    };

    setPrimaryFolders((prev) => [...prev, newFolder]);
  }, [
    getAllPrimaryFolderNames,
    generateUniqueName,
    generateUniqueId,
    clearValidationErrors,
  ]);

  const addSecondaryFolder = useCallback(
    (primaryFolderId: string) => {
      clearValidationErrors();
      setPrimaryFolders((prev) =>
        prev.map((primaryFolder) => {
          if (primaryFolder.id === primaryFolderId) {
            const existingNames = primaryFolder.secondaryFolders.map(
              (f) => f.name,
            );
            const newName = generateUniqueName(
              "Secondary Folder",
              existingNames,
            );

            const newSecondaryFolder: SecondaryFolder = {
              id: generateUniqueId(),
              name: newName,
              isEditing: false,
            };

            return {
              ...primaryFolder,
              secondaryFolders: [
                ...primaryFolder.secondaryFolders,
                newSecondaryFolder,
              ],
            };
          }
          return primaryFolder;
        }),
      );
    },
    [generateUniqueName, generateUniqueId, clearValidationErrors],
  );

  const editFolder = useCallback(
    (folderId: string, isSecondary: boolean, primaryFolderId?: string) => {
      clearValidationErrors();
      if (isSecondary && primaryFolderId) {
        setPrimaryFolders((prev) =>
          prev.map((primaryFolder) => {
            if (primaryFolder.id === primaryFolderId) {
              return {
                ...primaryFolder,
                secondaryFolders: primaryFolder.secondaryFolders.map(
                  (secondaryFolder) =>
                    secondaryFolder.id === folderId
                      ? { ...secondaryFolder, isEditing: true }
                      : { ...secondaryFolder, isEditing: false },
                ),
              };
            }
            return {
              ...primaryFolder,
              isEditing: false,
            };
          }),
        );
      } else {
        setPrimaryFolders((prev) =>
          prev.map((primaryFolder) => ({
            ...primaryFolder,
            isEditing: primaryFolder.id === folderId,
            secondaryFolders: primaryFolder.secondaryFolders.map((sf) => ({
              ...sf,
              isEditing: false,
            })),
          })),
        );
      }
    },
    [clearValidationErrors],
  );

  const saveFolder = useCallback(
    (
      folderId: string,
      newName: string,
      isSecondary: boolean,
      primaryFolderId?: string,
    ): boolean => {
      if (
        !validateFolderName(newName, folderId, isSecondary, primaryFolderId)
      ) {
        return false;
      }

      if (isSecondary && primaryFolderId) {
        setPrimaryFolders((prev) =>
          prev.map((primaryFolder) => {
            if (primaryFolder.id === primaryFolderId) {
              return {
                ...primaryFolder,
                secondaryFolders: primaryFolder.secondaryFolders.map(
                  (secondaryFolder) =>
                    secondaryFolder.id === folderId
                      ? {
                          ...secondaryFolder,
                          name: newName.trim(),
                          isEditing: false,
                        }
                      : secondaryFolder,
                ),
              };
            }
            return primaryFolder;
          }),
        );
      } else {
        setPrimaryFolders((prev) =>
          prev.map((primaryFolder) =>
            primaryFolder.id === folderId
              ? { ...primaryFolder, name: newName.trim(), isEditing: false }
              : primaryFolder,
          ),
        );
      }

      return true;
    },
    [validateFolderName],
  );

  const cancelEdit = useCallback(
    (folderId: string, isSecondary: boolean, primaryFolderId?: string) => {
      clearValidationErrors();
      if (isSecondary && primaryFolderId) {
        setPrimaryFolders((prev) =>
          prev.map((primaryFolder) => {
            if (primaryFolder.id === primaryFolderId) {
              return {
                ...primaryFolder,
                secondaryFolders: primaryFolder.secondaryFolders.map(
                  (secondaryFolder) =>
                    secondaryFolder.id === folderId
                      ? { ...secondaryFolder, isEditing: false }
                      : secondaryFolder,
                ),
              };
            }
            return primaryFolder;
          }),
        );
      } else {
        setPrimaryFolders((prev) =>
          prev.map((primaryFolder) =>
            primaryFolder.id === folderId
              ? { ...primaryFolder, isEditing: false }
              : primaryFolder,
          ),
        );
      }
    },
    [clearValidationErrors],
  );

  const deleteFolder = useCallback(
    (folderId: string, isSecondary: boolean, primaryFolderId?: string) => {
      clearValidationErrors();

      if (isSecondary && primaryFolderId) {
        const primaryFolder = primaryFolders.find(
          (f) => f.id === primaryFolderId,
        );
        if (primaryFolder && primaryFolder.secondaryFolders.length <= 1) {
          setValidation((prev) => ({
            ...prev,
            deleteLastSecondaryError:
              "Folder structure must contain at least one Primary and one Secondary Folder.",
          }));
          return;
        }

        setPrimaryFolders((prev) =>
          prev.map((primaryFolder) => {
            if (primaryFolder.id === primaryFolderId) {
              return {
                ...primaryFolder,
                secondaryFolders: primaryFolder.secondaryFolders.filter(
                  (secondaryFolder) => secondaryFolder.id !== folderId,
                ),
              };
            }
            return primaryFolder;
          }),
        );
      } else {
        if (primaryFolders.length > 1) {
          setPrimaryFolders((prev) =>
            prev.filter((folder) => folder.id !== folderId),
          );
        }
      }
    },
    [primaryFolders, clearValidationErrors],
  );

  const toggleExpand = useCallback((primaryFolderId: string) => {
    setPrimaryFolders((prev) =>
      prev.map((folder) =>
        folder.id === primaryFolderId
          ? { ...folder, expanded: !folder.expanded }
          : folder,
      ),
    );
  }, []);

  return {
    primaryFolders,
    validation,
    addPrimaryFolder,
    addSecondaryFolder,
    editFolder,
    saveFolder,
    cancelEdit,
    deleteFolder,
    toggleExpand,
  };
}
