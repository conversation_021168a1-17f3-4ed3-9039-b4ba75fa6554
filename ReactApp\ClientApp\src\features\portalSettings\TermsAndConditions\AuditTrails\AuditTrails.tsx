import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { ExportCsvFile } from "./components";
import { useAuditTrailsGridController } from "./hooks/useAuditTrailsGridController";
import AdvancedBaseGrid from "@/components/common/AdvancedBaseGrid/AdvancedBaseGrid";
import { useAuditTrailsAutoSuggest } from "./hooks/useAuditTrailsAutoSuggest";
import BaseGridFilterFactory from "@/components/smart/BaseGridFilterFactory/BaseGridFilterFactory";
import "./AuditTrails.scss";

export default function AuditTrails() {
  const {
    auditTrailsData,
    totalRecordCount,
    auditTrailsColumns,
    isDataLoading,
    isColumnsLoading,
    pagination,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
  } = useAuditTrailsGridController();

  return (
    <SectionLayout>
      <AdvancedBaseGrid
        totalRecordCount={totalRecordCount}
        columns={auditTrailsColumns}
        dataSource={auditTrailsData}
        filters={filters}
        skip={pagination.skip}
        take={pagination.take}
        onFilterChange={handleFilterChange}
        onPageChange={handlePageChange}
        onSortChange={handleSortChange}
        onRefresh={handleRefresh}
        isLoading={isDataLoading}
        sorts={sorts}
        isColumnsLoading={isColumnsLoading}
        actionsColumn={{
          label: "Acceptance History",
          renderer: (props) => <ExportCsvFile {...props} />,
        }}
        renderFilterFactory={(props, column) => (
          <BaseGridFilterFactory
            {...props}
            column={column}
            useAutoSuggestHook={useAuditTrailsAutoSuggest}
          />
        )}
      />
    </SectionLayout>
  );
}
