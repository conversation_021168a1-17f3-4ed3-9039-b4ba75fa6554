export interface ColumnFilterData {
  name: string;
  value: string;
}

export interface TypeGridColumn {
  key: string;
  displayValue: string;
  dataType: string;
  defaultVisible: boolean;
  sortable: boolean;
  filterType?: string;
  filterData?: ColumnFilterData[];
}

export interface ColumnConfig {
  key: string;
  displayValue: string;
  dataType: "text" | "numeric" | "date";
  filterType: "search" | "multiSelect" | "range" | "none";
  filterData?: { name: string; value: string }[];
}
