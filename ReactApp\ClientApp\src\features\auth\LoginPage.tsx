import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@progress/kendo-react-layout";
import { StackLayout } from "@progress/kendo-react-layout";
import { But<PERSON> } from "@progress/kendo-react-buttons";
import { useAuth } from "@/hooks/useAuth";
import { useOktaAuth } from "@okta/okta-react";
import { Navigate } from "react-router-dom";

export default function LoginPage() {
  const { login } = useAuth();
  const { authState } = useOktaAuth();

  if (authState?.isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <div className="k-d-flex k-justify-content-center k-align-items-center k-h-100vh">
      <Card style={{ width: 360 }}>
        <CardHeader className="k-text-center">
          <h1 className="k-card-title">Login Page</h1>
        </CardHeader>
        <CardBody>
          <StackLayout orientation="vertical" gap={16}>
            <Button themeColor="primary" size="large" onClick={login}>
              Login with Okta
            </Button>
          </StackLayout>
        </CardBody>
      </Card>
    </div>
  );
}
