import { Dialog, DialogActionsBar } from "@progress/kendo-react-dialogs";
import { Button } from "@progress/kendo-react-buttons";
import CreateTemplateForm from "./CreateTemplateForm";
import CreateTemplateTreeView from "./CreateTemplateTreeView";
import { useTemplateTreeView } from "../hooks/useTemplateTreeView";
import { Loader } from "@progress/kendo-react-indicators";
import type {
  CreateTemplateFormData,
  CreateTemplateValidation,
} from "../hooks/useCreateTemplatePopup";
import logger from "@/utils/logger";

interface CreateTemplatePopupProps {
  isOpen: boolean;
  formData: CreateTemplateFormData;
  validation: CreateTemplateValidation;
  isSubmitting: boolean;
  onFieldChange: (
    _field: keyof CreateTemplateFormData,
    _value: string | boolean,
  ) => void;
  onCreate: () => void;
  onCancel: () => void;
}

export default function CreateTemplatePopup({
  isOpen,
  formData,
  validation,
  isSubmitting,
  onFieldChange,
  onCreate,
  onCancel,
}: CreateTemplatePopupProps) {
  const {
    primaryFolders,
    validation: treeValidation,
    addPrimaryFolder,
    addSecondaryFolder,
    editFolder,
    saveFolder,
    cancelEdit,
    deleteFolder,
    toggleExpand,
  } = useTemplateTreeView();

  const isAnyFolderEditing = primaryFolders.some(
    (pf) => pf.isEditing || pf.secondaryFolders.some((sf) => sf.isEditing),
  );

  const isCreateDisabled =
    isSubmitting ||
    !formData.name.trim() ||
    !!validation.nameError ||
    !!treeValidation.duplicateNameError ||
    !!treeValidation.deleteLastSecondaryError ||
    isAnyFolderEditing;

  const handleCreateClick = () => {
    logger.info("Form Data:", formData);
    logger.info("Folder Structure:", primaryFolders);
    onCreate();
  };

  if (!isOpen) return null;

  return (
    <Dialog
      onClose={onCancel}
      title="Create Template"
      className="create-template-popup"
      width={950}
      height={450}
    >
      <div className="popup-content">
        <div className="popup-left">
          <CreateTemplateForm
            formData={formData}
            validation={validation}
            onFieldChange={onFieldChange}
          />
        </div>

        <div className="popup-right">
          <CreateTemplateTreeView
            primaryFolders={primaryFolders}
            validation={treeValidation}
            onAddPrimaryFolder={addPrimaryFolder}
            onAddSecondaryFolder={addSecondaryFolder}
            onEditFolder={editFolder}
            onSaveFolder={saveFolder}
            onCancelEdit={cancelEdit}
            onDeleteFolder={deleteFolder}
            onToggleExpand={toggleExpand}
          />
        </div>
      </div>

      <DialogActionsBar layout="end">
        <Button onClick={onCancel} disabled={isSubmitting} fillMode="flat">
          Cancel
        </Button>
        <Button
          themeColor="primary"
          onClick={handleCreateClick}
          disabled={isCreateDisabled}
        >
          {isSubmitting ? (
            <Loader size="small" type="infinite-spinner" themeColor="primary" />
          ) : (
            "Create"
          )}
        </Button>
      </DialogActionsBar>
    </Dialog>
  );
}
