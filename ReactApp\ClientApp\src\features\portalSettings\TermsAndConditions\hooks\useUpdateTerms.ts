import { useUpdateTermsMutation } from "@/api/termsApiSlice";
import logger from "@/utils/logger";

type UpdatePayload = {
  termsAndConditionsId: number;
  statementOfAgreement: string;
  triggerPoints: {
    promptAfterFirstLogin: boolean;
    promptAnnually: boolean;
    promptQuarterly: boolean;
    promptWhenUpdated: boolean;
  };
};

export const useUpdateTerms = () => {
  const [updateTerms, { isLoading }] = useUpdateTermsMutation();

  const update = async (
    payload: UpdatePayload,
    onError: (_message: string) => void,
    onSuccess?: (_msg?: string) => void,
  ): Promise<void> => {
    try {
      const response = await updateTerms(payload).unwrap();
      logger.info("UpdateTerms success", response);
      onSuccess?.(response.message);
    } catch (error: any) {
      logger.error("UpdateTerms failed", error);
      const message =
        error?.data?.message || error?.message || "Update failed.";
      onError(message);
    }
  };

  return { update, isUpdating: isLoading };
};
