import { useState, useCallback } from "react";
import type {
  CompositeFilterDescriptor,
  SortDescriptor,
} from "@progress/kendo-data-query";
import type {
  GridPageChangeEvent,
  GridFilterChangeEvent,
  GridSortChangeEvent,
} from "@progress/kendo-react-grid";
import { useTemplateGridColumns } from "./useTemplateGridColumns";
import { useTemplateList } from "./useTemplateList";

export const useTemplateGridController = () => {
  const [skip, setSkip] = useState(0);
  const [take, setTake] = useState(10);
  const [filters, setFilters] = useState<CompositeFilterDescriptor>({
    logic: "and",
    filters: [],
  });
  const [sorts, setSorts] = useState<SortDescriptor[]>([]);

  const { columns, isColumnsLoading } = useTemplateGridColumns();

  const normalizeFilters = (
    filters: CompositeFilterDescriptor,
  ): CompositeFilterDescriptor => {
    const normalizedFilters = filters.filters.flatMap((f: any) => {
      if (Array.isArray(f.value)) {
        return f.value.map((val: string) => ({
          field: f.field,
          operator: f.operator,
          value: val,
        }));
      }
      return [f];
    });

    return {
      logic: "or",
      filters: normalizedFilters,
    };
  };

  const {
    templateList,
    isLoading: isTemplatesLoading,
    totalRecordCount,
  } = useTemplateList({
    skip,
    take,
    filters: normalizeFilters(filters),
    sorts,
  });

  const handlePageChange = useCallback((event: GridPageChangeEvent) => {
    setSkip(event.page.skip);
    setTake(event.page.take);
  }, []);

  const handleSortChange = useCallback((event: GridSortChangeEvent) => {
    setSorts(event.sort);
    setSkip(0);
  }, []);

  const handleFilterChange = useCallback((event: GridFilterChangeEvent) => {
    setFilters(event.filter);
    setSkip(0);
  }, []);

  const handleRefresh = useCallback(() => {
    setFilters({ logic: "and", filters: [] });
    setSorts([]);
    setSkip(0);
    setTake(10);
  }, []);

  return {
    templateList,
    isTemplatesLoading,
    totalRecordCount,
    columns,
    isColumnsLoading,
    pagination: { skip, take },
    filters,
    sorts,
    handlePageChange,
    handleSortChange,
    handleFilterChange,
    handleRefresh,
  };
};
