import { type GridCustomCellProps } from "@progress/kendo-react-grid";
import type { Template } from "@/types/templates";
import { TrashIcon, PencilSquareIcon } from "@heroicons/react/24/outline";
import "../Templates.scss";

interface TemplatesGridActionsProps extends GridCustomCellProps {
  dataItem: Template;
  onEdit: (template: Template) => void;
  onDelete: () => void;
}

const TemplatesGridActions = ({
  dataItem,
  onEdit,
  onDelete,
}: TemplatesGridActionsProps) => {
  return (
    <td className="actions-cell">
      <PencilSquareIcon onClick={() => onEdit(dataItem)} type="button" height={20} width={20} />
      <TrashIcon onClick={onDelete} type="button" height={20} width={20} />
    </td>
  );
};

export default TemplatesGridActions;
