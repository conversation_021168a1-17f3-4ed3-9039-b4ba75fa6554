import type {
  CompositeFilterDescriptor,
  FilterDescriptor,
} from "@progress/kendo-data-query";

export function getFilterDescriptors(
  filter: CompositeFilterDescriptor,
): FilterDescriptor[] {
  if (!filter) return [];

  const filters: FilterDescriptor[] = [];

  (filter.filters || []).forEach((f: any) => {
    if (f.filters) {
      filters.push(...getFilterDescriptors(f));
    } else {
      filters.push(f);
    }
  });

  return filters;
}
