import React from "react";
import { configureStore } from "@reduxjs/toolkit";
import { Provider } from "react-redux";
import { MemoryRouter } from "react-router-dom";
import { render } from "@testing-library/react";
import type { ReactElement } from "react";
import appReducer from "@/appSlice";
import { sampleApiSlice } from "@/api/sampleApiSlice";
import { recentActivityApiSlice } from "@/api/recentActivityApiSlice";
import { todoApiSlice } from "@/api/todoApiSlice";
import { termsApiSlice } from "@/api/termsApiSlice";
import { auditTrailsApiSlice } from "@/api/auditTrailsApiSlice";

// Create a test store
export function createTestStore(preloadedState = {}) {
  return configureStore({
    reducer: {
      app: appReducer,
      [sampleApiSlice.reducerPath]: sampleApiSlice.reducer,
      [todoApiSlice.reducerPath]: todoApiSlice.reducer,
      [termsApiSlice.reducerPath]: termsApiSlice.reducer,
      [auditTrailsApiSlice.reducerPath]: auditTrailsApiSlice.reducer,
      [recentActivityApiSlice.reducerPath]: recentActivityApiSlice.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware()
        .concat(sampleApiSlice.middleware)
        .concat(todoApiSlice.middleware)
        .concat(termsApiSlice.middleware)
        .concat(auditTrailsApiSlice.middleware)
        .concat(recentActivityApiSlice.middleware),
    preloadedState,
  });
}

// Test wrapper with Redux Provider and Router
export function renderWithProviders(
  ui: ReactElement,
  {
    preloadedState = {},
    store = createTestStore(preloadedState),
    ...renderOptions
  } = {}
) {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <Provider store={store}>
        <MemoryRouter>
          {children}
        </MemoryRouter>
      </Provider>
    );
  }

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) };
}
