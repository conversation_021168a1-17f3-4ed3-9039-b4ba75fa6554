import { AppBar, AppBarSection } from "@progress/kendo-react-layout";
import "./Footer.scss";

const linkStyle = {
  color: "#007bff",
  textDecoration: "underline",
  fontSize: "0.9rem",
};

export default function Footer() {
  return (
    <AppBar positionMode="static" className="footer-bar">
      <AppBarSection className="footer-left">
        <span>© IRIS Software Group Ltd 2025</span>
      </AppBarSection>
      <AppBarSection className="footer-right">
        <a href="/privacy" style={linkStyle}>
          Privacy
        </a>
        <a href="/security" style={linkStyle}>
          Security
        </a>
        <a href="/terms" style={linkStyle}>
          Terms of Service
        </a>
      </AppBarSection>
    </AppBar>
  );
}
