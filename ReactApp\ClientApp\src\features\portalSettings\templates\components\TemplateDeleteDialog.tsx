import { Button } from "@progress/kendo-react-buttons";
import { Dialog, DialogActionsBar } from "@progress/kendo-react-dialogs";

interface TemplateDeleteDialogProps {
  open: boolean;
  onClose: () => void;
}

const TemplateDeleteDialog = ({ open, onClose }: TemplateDeleteDialogProps) => {
  return (
    <>
      {open && (
        <Dialog onClose={onClose} closeIcon={true} title="">
          <p style={{ margin: "25px", textAlign: "center" }}>
            Are you sure you want to delete the Template? This action is not
            reversible.
          </p>
          <DialogActionsBar>
            <Button type="button" onClick={onClose}>
              Yes
            </Button>
            <Button type="button" onClick={onClose} themeColor="primary">
              No
            </Button>
          </DialogActionsBar>
        </Dialog>
      )}
    </>
  );
};

export default TemplateDeleteDialog;
