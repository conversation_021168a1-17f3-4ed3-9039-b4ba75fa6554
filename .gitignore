# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Node.js / Frontend
node_modules/
dist/
dist-ssr/
*.local
coverage/
.vscode/*
!.vscode/extensions.json

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
bin/
Bin/
obj/
Obj/
.vs/
wwwroot/dist/

# User-specific files
*.suo
*.user
*.userosscache
*.sln.docstates
*.userprefs
*.ntvs*
*.njsproj
*.sln
*.sw?

# Visual Studio / JetBrains Rider / MonoDevelop
.idea/
*.aps
*.ncb
*.opt
*.plg
*.vspscc
*.vssscc
*.cachefile
*.dbmdl
*.dbproj.schemaview
*.GhostDoc.xml
*.pidb
*.svclog
*.scc
*.psess
*.vsp
*.vspx
*.sap
*.pfx
*.publishsettings
*.builds
*.ntvs_analysis.dat
*.tmp
*.tmp_proj
.DS_Store

# Roslyn / ReSharper / JustCode / NCrunch / Code Coverage
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user
.JustCode
_TeamCity*
*.dotCover
_NCrunch_*
.*crunch*.local.xml
nCrunchTemp_*

# Chutzpah Test files
_Chutzpah*

# Output folders
publish/
AppPackages/
BundleArtifacts/
ClientBin/
csx/
ecf/
rcf/
FakesAssemblies/
Generated_Code/
_Pvt_Extensions
UpgradeLog*.*
Backup*/
_UpgradeReport_Files/

# Azure / NuGet / ClickOnce / Paket / FAKE
*.azurePubxml
*.pubxml
*.publishproj
*.nupkg
**/packages/*
!**/packages/build/
.paket/paket.exe
.fake/
ApplicationInsights.config

# SQL / BI / Orleans / LightSwitch
*.mdf
*.ldf
*.rdl.data
*.bim.layout
*.bim_*.settings
orleans.codegen.cs
**/*.HTMLClient/GeneratedArtifacts
**/*.DesktopClient/GeneratedArtifacts
**/*.DesktopClient/ModelManifest.xml
**/*.Server/GeneratedArtifacts
**/*.Server/ModelManifest.xml

# Sass / Web Workbench
.sass-cache/

# Backup & Temp
~$*
*~
*.bak
