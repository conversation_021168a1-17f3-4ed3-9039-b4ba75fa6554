import { useTranslation } from "react-i18next";
import { Button } from "@progress/kendo-react-buttons";
import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { SampleForm, SamplePreview } from "./components";
import { useSampleFeatureController } from "./hooks/useSampleFeatureController";

export default function SampleFeaturePage() {
  const { t } = useTranslation("dashboard");

  const {
    name,
    description,
    isSaving,
    errorMessage,
    successMessage,
    handleChange,
    handleSave,
    onCloseError,
    onCloseSuccess,
  } = useSampleFeatureController();

  return (
    <SectionLayout
      isSaving={isSaving}
      errorMessage={errorMessage}
      onCloseError={onCloseError}
      successMessage={successMessage}
      onCloseSuccess={onCloseSuccess}
      footer={
        <Button themeColor="primary" onClick={handleSave} disabled={isSaving}>
          {isSaving ? t("btn.saving") || "Saving..." : t("btn.save") || "Save"}
        </Button>
      }
    >
      <SampleForm
        name={name}
        description={description}
        onChange={handleChange}
      />

      <SamplePreview name={name} description={description} />
    </SectionLayout>
  );
}
