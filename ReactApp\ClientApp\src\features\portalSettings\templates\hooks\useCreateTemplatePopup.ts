import logger from "@/utils/logger";
import { useState } from "react";

export interface CreateTemplateFormData {
  name: string;
  description: string;
  isActive: boolean;
}

export interface CreateTemplateValidation {
  nameError: string;
}

export interface SecondaryFolder {
  id: string;
  name: string;
  isEditing?: boolean;
}

export interface PrimaryFolder {
  id: string;
  name: string;
  isEditing?: boolean;
  expanded?: boolean;
  secondaryFolders: SecondaryFolder[];
}

export interface TreeValidation {
  duplicateNameError: string;
  deleteLastSecondaryError: string;
}

export function useCreateTemplatePopup() {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState<CreateTemplateFormData>({
    name: "",
    description: "",
    isActive: true,
  });
  const [validation, setValidation] = useState<CreateTemplateValidation>({
    nameError: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const openPopup = () => {
    setIsOpen(true);
    setFormData({
      name: "",
      description: "",
      isActive: true,
    });
    setValidation({
      nameError: "",
    });
  };

  const closePopup = () => {
    setIsOpen(false);
    setIsSubmitting(false);
  };

  const updateField = (
    field: keyof CreateTemplateFormData,
    value: string | boolean,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    if (field === "name" && validation.nameError) {
      setValidation((prev) => ({
        ...prev,
        nameError: "",
      }));
    }
  };

  const validateForm = (): boolean => {
    const errors: CreateTemplateValidation = {
      nameError: "",
    };

    if (!formData.name.trim()) {
      errors.nameError = "Template Name is mandatory";
    }

    setValidation(errors);

    return !errors.nameError;
  };

  const handleCreate = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      logger.info("Creating template:", formData);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      closePopup();
    } catch (error) {
      logger.error("Failed to create template:", error as Record<string, any>);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    closePopup();
  };

  return {
    isOpen,
    formData,
    validation,
    isSubmitting,
    openPopup,
    closePopup,
    updateField,
    handleCreate,
    handleCancel,
  };
}
