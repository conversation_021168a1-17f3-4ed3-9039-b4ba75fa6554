import logger from "@/utils/logger";
import { useState } from "react";
import type { Template } from "@/types/templates";

export interface CreateTemplateFormData {
  name: string;
  description: string;
  isActive: boolean;
}

export interface CreateTemplateValidation {
  nameError: string;
}

export interface SecondaryFolder {
  id: string;
  name: string;
  isEditing?: boolean;
  isExisting?: boolean; // True for folders that existed before edit session
}

export interface PrimaryFolder {
  id: string;
  name: string;
  isEditing?: boolean;
  expanded?: boolean;
  isExisting?: boolean; // True for folders that existed before edit session
  secondaryFolders: SecondaryFolder[];
}

export interface TreeValidation {
  duplicateNameError: string;
  deleteLastSecondaryError: string;
}

export function useCreateTemplatePopup() {
  const [isOpen, setIsOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
  const [formData, setFormData] = useState<CreateTemplateFormData>({
    name: "",
    description: "",
    isActive: true,
  });
  const [validation, setValidation] = useState<CreateTemplateValidation>({
    nameError: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const openPopup = () => {
    setIsOpen(true);
    setIsEditMode(false);
    setEditingTemplate(null);
    setFormData({
      name: "",
      description: "",
      isActive: true,
    });
    setValidation({
      nameError: "",
    });
  };

  const openEditPopup = (template: Template) => {
    setIsOpen(true);
    setIsEditMode(true);
    setEditingTemplate(template);
    setFormData({
      name: template.templateName,
      description: template.description,
      isActive: template.status === "Active",
    });
    setValidation({
      nameError: "",
    });
  };

  const closePopup = () => {
    setIsOpen(false);
    setIsSubmitting(false);
    setIsEditMode(false);
    setEditingTemplate(null);
  };

  const updateField = (
    field: keyof CreateTemplateFormData,
    value: string | boolean,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    if (field === "name" && validation.nameError) {
      setValidation((prev) => ({
        ...prev,
        nameError: "",
      }));
    }
  };

  const validateForm = (): boolean => {
    const errors: CreateTemplateValidation = {
      nameError: "",
    };

    if (!formData.name.trim()) {
      errors.nameError = "Template Name is mandatory";
    }

    setValidation(errors);

    return !errors.nameError;
  };

  const handleCreate = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      logger.info("Creating template:", formData);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      closePopup();
    } catch (error) {
      logger.error("Failed to create template:", error as Record<string, any>);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    closePopup();
  };

  return {
    isOpen,
    isEditMode,
    editingTemplate,
    formData,
    validation,
    isSubmitting,
    openPopup,
    openEditPopup,
    closePopup,
    updateField,
    handleCreate,
    handleCancel,
  };
}
