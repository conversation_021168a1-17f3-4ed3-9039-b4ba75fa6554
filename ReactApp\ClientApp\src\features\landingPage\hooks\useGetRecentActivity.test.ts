import { renderHook } from "@testing-library/react";
import { vi } from "vitest";
import { useGetRecentActivity } from "./useGetRecentActivity";

// Mock the RTK Query hook
const mockUseGetRecentActivityQuery = vi.fn();
vi.mock("@/api/recentActivityApiSlice", () => ({
  useGetRecentActivityQuery: () => mockUseGetRecentActivityQuery(),
}));

describe("useGetRecentActivity", () => {
  beforeEach(() => {
    mockUseGetRecentActivityQuery.mockClear();
  });

  it("returns data from RTK Query hook", () => {
    const mockData = {
      records: [
        {
          id: "1",
          setupArea: "DMS Settings" as const,
          section: "Test Section",
          updatedBy: "Test User",
          lastUpdated: "2025-01-01",
        },
      ],
      pageCount: 1,
      pageNumber: 1,
      pageSize: 20,
      totalRecordCount: 1,
    };

    const mockRefetch = vi.fn();
    const mockError = null;
    const mockParams = {
      pageNumber: 1,
      pageSize: 20,
      sortField: "lastUpdated",
      sortDirection: "desc",
    };

    mockUseGetRecentActivityQuery.mockReturnValue({
      data: mockData,
      isLoading: false,
      isFetching: false,
      error: mockError,
      refetch: mockRefetch,
    });

    const { result } = renderHook(() => useGetRecentActivity(mockParams));

    expect(result.current.recentActivity).toBe(mockData);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isFetching).toBe(false);
    expect(result.current.error).toBe(mockError);
    expect(result.current.refetchRecentActivity).toBe(mockRefetch);
  });

  it("returns loading state correctly", () => {
    const mockParams = {
      pageNumber: 1,
      pageSize: 20,
      sortField: "lastUpdated",
      sortDirection: "desc",
    };

    mockUseGetRecentActivityQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      isFetching: false,
      error: null,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useGetRecentActivity(mockParams));

    expect(result.current.isLoading).toBe(true);
    expect(result.current.isFetching).toBe(false);
    expect(result.current.recentActivity).toBeUndefined();
  });

  it("returns fetching state correctly", () => {
    const mockParams = {
      pageNumber: 1,
      pageSize: 20,
      sortField: "lastUpdated",
      sortDirection: "desc",
    };

    mockUseGetRecentActivityQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      isFetching: true,
      error: null,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useGetRecentActivity(mockParams));

    expect(result.current.isLoading).toBe(false);
    expect(result.current.isFetching).toBe(true);
  });

  it("returns error state correctly", () => {
    const mockParams = {
      pageNumber: 1,
      pageSize: 20,
      sortField: "lastUpdated",
      sortDirection: "desc",
    };
    const mockError = { message: "API Error", status: 500 };

    mockUseGetRecentActivityQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      isFetching: false,
      error: mockError,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useGetRecentActivity(mockParams));

    expect(result.current.error).toBe(mockError);
    expect(result.current.recentActivity).toBeUndefined();
  });

  it("returns refetch function correctly", () => {
    const mockParams = {
      pageNumber: 1,
      pageSize: 20,
      sortField: "lastUpdated",
      sortDirection: "desc",
    };
    const mockRefetch = vi.fn();

    mockUseGetRecentActivityQuery.mockReturnValue({
      data: null,
      isLoading: false,
      isFetching: false,
      error: null,
      refetch: mockRefetch,
    });

    const { result } = renderHook(() => useGetRecentActivity(mockParams));

    expect(result.current.refetchRecentActivity).toBe(mockRefetch);
    expect(typeof result.current.refetchRecentActivity).toBe("function");
  });

  it("handles undefined data gracefully", () => {
    const mockParams = {
      pageNumber: 1,
      pageSize: 20,
      sortField: "lastUpdated",
      sortDirection: "desc",
    };

    mockUseGetRecentActivityQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      isFetching: false,
      error: null,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useGetRecentActivity(mockParams));

    expect(result.current.recentActivity).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isFetching).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it("handles null data gracefully", () => {
    mockUseGetRecentActivityQuery.mockReturnValue({
      data: null,
      isLoading: false,
      isFetching: false,
      error: null,
      refetch: vi.fn(),
    });

    const mockParams = {
      pageNumber: 1,
      pageSize: 20,
      sortField: "lastUpdated",
      sortDirection: "desc",
    };

    const { result } = renderHook(() => useGetRecentActivity(mockParams));

    expect(result.current.recentActivity).toBe(null);
  });

  it("maintains consistent return structure", () => {
    mockUseGetRecentActivityQuery.mockReturnValue({
      data: null,
      isLoading: false,
      isFetching: false,
      error: null,
      refetch: vi.fn(),
    });

    const mockParams = {
      pageNumber: 1,
      pageSize: 20,
      sortField: "lastUpdated",
      sortDirection: "desc",
    };

    const { result } = renderHook(() => useGetRecentActivity(mockParams));

    // Check that all expected properties are present
    expect(result.current).toHaveProperty("recentActivity");
    expect(result.current).toHaveProperty("isLoading");
    expect(result.current).toHaveProperty("isFetching");
    expect(result.current).toHaveProperty("error");
    expect(result.current).toHaveProperty("refetchRecentActivity");
  });

  it("correctly maps RTK Query properties to custom names", () => {
    const mockData = { data: [], total: 0 };
    const mockRefetch = vi.fn();
    const mockError = { message: "Test error" };

    mockUseGetRecentActivityQuery.mockReturnValue({
      data: mockData,
      isLoading: true,
      isFetching: true,
      error: mockError,
      refetch: mockRefetch,
    });

    const mockParams = {
      pageNumber: 1,
      pageSize: 20,
      sortField: "lastUpdated",
      sortDirection: "desc",
    };

    const { result } = renderHook(() => useGetRecentActivity(mockParams));

    // Verify the mapping from RTK Query names to custom names
    expect(result.current.recentActivity).toBe(mockData); // data -> recentActivity
    expect(result.current.isLoading).toBe(true); // isLoading -> isLoading
    expect(result.current.isFetching).toBe(true); // isFetching -> isFetching
    expect(result.current.error).toBe(mockError); // error -> error
    expect(result.current.refetchRecentActivity).toBe(mockRefetch); // refetch -> refetchRecentActivity
  });
});
