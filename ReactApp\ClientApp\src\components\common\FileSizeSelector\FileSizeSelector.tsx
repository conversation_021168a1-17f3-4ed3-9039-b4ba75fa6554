import {
  NumericTextBox,
  type NumericTextBoxChangeEvent,
  type NumericTextBoxHandle,
} from "@progress/kendo-react-inputs";
import { Button } from "@progress/kendo-react-buttons";
import { filterClearIcon } from "@progress/kendo-svg-icons";
import { useRef } from "react";
import "./FileSizeSelector.scss";

interface FileSizeSelectorProps {
  value?: { start?: number; end?: number } | null;
  onChange: (_event: {
    value: any;
    operator: string;
    syntheticEvent: any;
  }) => void;
}

const FileSizeSelector = ({
  value = null,
  onChange,
}: FileSizeSelectorProps) => {
  const minRef = useRef<NumericTextBoxHandle>(null);
  const maxRef = useRef<NumericTextBoxHandle>(null);

  const handleChange = (event: NumericTextBoxChangeEvent) => {
    const min = minRef.current?.value ?? null;
    const max = maxRef.current?.value ?? null;

    if (min == null && max == null) return;

    onChange({
      value: { start: min, end: max },
      operator: "intValueRange",
      syntheticEvent: event.syntheticEvent,
    });
  };

  const handleClear = (event: any) => {
    event.preventDefault();
    onChange({
      value: null,
      operator: "",
      syntheticEvent: event,
    });
  };

  return (
    <div className="gridCellFilterContainer">
      <NumericTextBox
        value={value?.start ?? null}
        ref={minRef}
        placeholder="Min"
        onChange={handleChange}
        min={0}
        width={75}
      />
      <NumericTextBox
        value={value?.end ?? null}
        ref={maxRef}
        placeholder="Max"
        onChange={handleChange}
        min={0}
        width={75}
      />
      <Button
        svgIcon={filterClearIcon}
        title="Clear"
        disabled={!value}
        onClick={handleClear}
        type="button"
      />
    </div>
  );
};

export default FileSizeSelector;
