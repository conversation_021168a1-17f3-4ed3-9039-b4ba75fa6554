import {
  Grid,
  GridColumn,
  type GridCustomCellProps,
  type GridCustomFilterCellProps,
  type GridFilterChangeEvent,
  type GridPageChangeEvent,
  type GridSortChangeEvent,
} from "@progress/kendo-react-grid";
import type {
  CompositeFilterDescriptor,
  SortDescriptor,
} from "@progress/kendo-data-query";
import type { ReactNode } from "react";
import GridPagination from "../GridPagination/GridPagination";
import BaseGridAppliedFilters from "@/components/smart/BaseGridAppliedFilters/BaseGridAppliedFilters";
import "./AdvancedBaseGrid.scss";

interface AdvancedBaseGridProps {
  columns: any[];
  dataSource: any[];
  filters: CompositeFilterDescriptor;
  skip: number;
  take: number;
  sorts: SortDescriptor[];
  onPageChange: (_event: GridPageChangeEvent) => void;
  onFilterChange: (_event: GridFilterChangeEvent) => void;
  onSortChange: (_event: GridSortChangeEvent) => void;
  onRefresh: () => void;
  totalRecordCount: number;
  isLoading: boolean;
  isColumnsLoading: boolean;
  actionsColumn?: {
    label: string;
    renderer: (_props: GridCustomCellProps) => ReactNode;
  };
  dataCellMapper?: Record<string, any>;
  filterCellMapper?: Record<string, any>;
  renderFilterFactory?: (
    _props: GridCustomFilterCellProps,
    _column: any,
  ) => ReactNode;
}

const AdvancedBaseGrid = ({
  columns,
  dataSource,
  filters,
  skip,
  take,
  sorts,
  onFilterChange,
  onPageChange,
  onSortChange,
  onRefresh,
  totalRecordCount,
  isLoading,
  isColumnsLoading,
  actionsColumn,
  dataCellMapper = {},
  filterCellMapper = {},
  renderFilterFactory,
}: AdvancedBaseGridProps) => {
  return (
    <div className="advanced-base-grid">
      <BaseGridAppliedFilters filters={filters} />
      <Grid
        className="advanced-base-grid"
        data={dataSource}
        skip={skip}
        take={take}
        total={totalRecordCount}
        pageable
        sortable
        filterable
        filter={filters}
        sort={sorts}
        onPageChange={onPageChange}
        onSortChange={onSortChange}
        onFilterChange={onFilterChange}
        showLoader={isLoading || isColumnsLoading}
        pager={(props) => (
          <GridPagination
            {...props}
            total={totalRecordCount}
            onRefresh={onRefresh}
            onPageChange={({ page }) =>
              onPageChange({ page } as GridPageChangeEvent)
            }
          />
        )}
      >
        {columns.map((column) => (
          <GridColumn
            key={column.key}
            field={column.key}
            title={column.displayValue}
            editor="text"
            filter={
              column.filterType === "search"
                ? "text"
                : column.filterType === "range"
                  ? "date"
                  : undefined
            }
            sortable={column.sortable}
            filterable={column.filterType !== "none"}
            cells={{
              filterCell: filterCellMapper?.[column.key]
                ? (props) => filterCellMapper[column.key](props, column)
                : renderFilterFactory
                  ? (props) =>
                      renderFilterFactory(
                        props as GridCustomFilterCellProps,
                        column,
                      )
                  : undefined,
              data: dataCellMapper?.[column.key],
            }}
          />
        ))}

        {actionsColumn && (
          <GridColumn
            title={actionsColumn.label}
            filterable={false}
            columnMenu={undefined}
            cells={{
              data: actionsColumn.renderer,
              filterCell: undefined,
            }}
          />
        )}
      </Grid>
    </div>
  );
};

export default AdvancedBaseGrid;
