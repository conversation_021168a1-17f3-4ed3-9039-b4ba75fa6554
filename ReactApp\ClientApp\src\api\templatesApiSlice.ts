import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "./interceptorsSlice";
import type { TypeGridColumn } from "@/types/column";
import { mockTemplateColumns, mockTemplatesData } from "./mocks/templatesMock";
import type { TemplateListResponse, TemplateNodesResponse } from "@/types/templates";
import {
  filterBy,
  orderBy,
  type CompositeFilterDescriptor,
  type SortDescriptor,
} from "@progress/kendo-data-query";
import config from "@/config";

interface GetTemplateListParams {
  skip: number;
  take: number;
  filters: CompositeFilterDescriptor;
  sorts: SortDescriptor[];
}

// Mock data for template nodes
const mockTemplateNodes = [
  {
    id: 1,
    name: "Node 1",
    totalChildNodes: 1,
    childNodes: [
      { id: 2, name: "Sub Node 1" }
    ]
  },
  {
    id: 3,
    name: "Node 2",
    totalChildNodes: 1,
    childNodes: [
      { id: 4, name: "Sub Node 2" }
    ]
  }
];

export const templatesApiSlice = createApi({
  reducerPath: "templatesApi",
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getTemplatesGridColumns: builder.query<TypeGridColumn[], void>({
      queryFn: async () => {
        await new Promise((resolve) => setTimeout(resolve, 500));
        return { data: mockTemplateColumns };
      },
    }),
    getTemplateList: builder.query<TemplateListResponse, GetTemplateListParams>(
      {
        queryFn: async ({ skip, take, filters, sorts }) => {
          await new Promise((resolve) => setTimeout(resolve, 500));

          let currentData = [...mockTemplatesData.records];

          currentData = filterBy(currentData, filters);
          currentData = orderBy(currentData, sorts || []);

          const totalRecordCount = currentData.length;
          const pageNumber = Math.floor(skip / take) + 1;
          const pageCount = Math.ceil(totalRecordCount / take);
          const records = currentData.slice(skip, skip + take);

          return {
            data: {
              records,
              totalRecordCount,
              pageCount,
              pageNumber,
              pageSize: take,
            },
          };
        },
      },
    ),
    getTemplateNodes: builder.query<TemplateNodesResponse, number>({
      queryFn: async (templateId) => {
        if (config.featureFlags.ui.UPDATE_TEMPLATE) {
          await new Promise((resolve) => setTimeout(resolve, 500));
          return { data: { nodes: mockTemplateNodes } };
        }

        // For real API call when feature flag is disabled
        // const result = await baseQuery({
        //   url: `api/PortalBinderTemplates/${templateId}/nodes`,
        //   method: "GET",
        // });
        // return result as QueryReturnValue<TemplateNodesResponse, FetchBaseQueryError, {}>;

        // For now, return mock data even when feature flag is disabled
        await new Promise((resolve) => setTimeout(resolve, 500));
        return { data: { nodes: mockTemplateNodes } };
      },
    }),
  }),
});

export const {
  useGetTemplatesGridColumnsQuery,
  useGetTemplateListQuery,
  useGetTemplateNodesQuery
} = templatesApiSlice;
