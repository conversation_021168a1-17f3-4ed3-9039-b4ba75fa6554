import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "./interceptorsSlice";
import type { TypeGridColumn } from "@/types/column";
import { mockTemplateColumns, mockTemplatesData } from "./mocks/templatesMock";
import type { TemplateListResponse } from "@/types/templates";
import {
  filterBy,
  orderBy,
  type CompositeFilterDescriptor,
  type SortDescriptor,
} from "@progress/kendo-data-query";

interface GetTemplateListParams {
  skip: number;
  take: number;
  filters: CompositeFilterDescriptor;
  sorts: SortDescriptor[];
}

export const templatesApiSlice = createApi({
  reducerPath: "templatesApi",
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getTemplatesGridColumns: builder.query<TypeGridColumn[], void>({
      queryFn: async () => {
        await new Promise((resolve) => setTimeout(resolve, 500));
        return { data: mockTemplateColumns };
      },
    }),
    getTemplateList: builder.query<TemplateListResponse, GetTemplateListParams>(
      {
        queryFn: async ({ skip, take, filters, sorts }) => {
          await new Promise((resolve) => setTimeout(resolve, 500));

          let currentData = [...mockTemplatesData.records];

          currentData = filterBy(currentData, filters);
          currentData = orderBy(currentData, sorts || []);

          const totalRecordCount = currentData.length;
          const pageNumber = Math.floor(skip / take) + 1;
          const pageCount = Math.ceil(totalRecordCount / take);
          const records = currentData.slice(skip, skip + take);

          return {
            data: {
              records,
              totalRecordCount,
              pageCount,
              pageNumber,
              pageSize: take,
            },
          };
        },
      },
    ),
  }),
});

export const { useGetTemplatesGridColumnsQuery, useGetTemplateListQuery } =
  templatesApiSlice;
