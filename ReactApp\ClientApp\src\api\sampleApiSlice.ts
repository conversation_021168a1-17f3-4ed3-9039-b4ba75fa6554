import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const sampleApiSlice = createApi({
  reducerPath: "sampleApi",
  baseQuery: fetchBaseQuery({ baseUrl: "/api" }),
  tagTypes: ["Sample"],
  endpoints: (builder) => ({
    getSample: builder.query<any, void>({
      query: () => "/sample",
      providesTags: ["Sample"],
    }),
    saveSample: builder.mutation<void, any>({
      query: (body) => ({
        url: "/sample",
        method: "POST",
        body,
      }),
      invalidatesTags: ["Sample"],
    }),
    updateSample: builder.mutation<{ message: string }, any>({
      query: (body) => ({
        url: `/sample/${body.id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["Sample"],
    }),
  }),
});

export const {
  useGetSampleQuery,
  useSaveSampleMutation,
  useUpdateSampleMutation,
} = sampleApiSlice;
