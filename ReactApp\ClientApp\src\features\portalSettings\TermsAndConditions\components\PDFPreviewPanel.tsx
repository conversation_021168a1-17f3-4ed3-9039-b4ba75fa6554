import { useTranslation } from "react-i18next";
import { useEffect } from "react";
import { PDFViewer } from "@progress/kendo-react-pdf-viewer";
import { Button } from "@progress/kendo-react-buttons";
import {
  ArrowDownTrayIcon,
  ArrowPathIcon,
  PrinterIcon,
} from "@heroicons/react/24/outline";

interface Props {
  isSaving: boolean;
  onSave: () => void;
  file: File | null;
  remoteBlobUrl: string | null;
  previewBlobUrl?: string | null;
  terms?: { file?: { documentUrl?: string } };
  previewStatement?: string | null;
  initialStatement?: string;
}

export default function PDFPreviewPanel({
  remoteBlobUrl,
  previewBlobUrl,
  previewStatement,
  initialStatement,
}: Props) {
  const { t } = useTranslation("dashboard");

  const viewerUrl = previewBlobUrl || remoteBlobUrl || null;

  const viewerStyle = {
    width: "100%",
    height: "400px",
    border: "1px solid #e0e0e0",
  };

  useEffect(() => {
    return () => {
      if (previewBlobUrl) {
        URL.revokeObjectURL(previewBlobUrl);
      }
    };
  }, [previewBlobUrl]);

  return (
    <div className="pdf-preview-panel">
      <div className="pdf-viewer">
        {viewerUrl ? (
          <PDFViewer
            url={viewerUrl}
            defaultZoom={0.5}
            style={viewerStyle}
            tools={["pager", "print", "download", "selection", "zoomInOut"]}
          />
        ) : (
          <div className="no-preview">{t("preview.noPreview")}</div>
        )}
      </div>

      <div className="preview-text">
        {previewStatement?.trim() ||
          initialStatement?.trim() ||
          t("preview.text")}
      </div>

      <div className="preview-footer">
        <div className="preview-links disabled-preview">
          <a className="preview-link" aria-disabled="true">
            <ArrowPathIcon />
            <span>{t("preview.refresh")}</span>
          </a>
          <a className="preview-link" aria-disabled="true">
            <ArrowDownTrayIcon />
            <span>{t("preview.download")}</span>
          </a>
          <a className="preview-link" aria-disabled="true">
            <PrinterIcon />
            <span>{t("preview.print")}</span>
          </a>
        </div>

        <div className="disabled-preview">
          <Button themeColor="base" size="small" disabled>
            {t("btn.agree")}
          </Button>
        </div>
      </div>
    </div>
  );
}
