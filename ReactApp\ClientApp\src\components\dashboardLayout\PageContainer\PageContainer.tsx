type PageContainerProps = {
  children: React.ReactNode;
  title?: string;
  className?: string;
};

export default function PageContainer({
  children,
  title,
  className = "",
}: PageContainerProps) {
  return (
    <div
      className={className}
      style={{
        padding: "2rem",
        flex: 1,
        display: "flex",
        flexDirection: "column",
        overflowY: "auto",
      }}
    >
      {title && (
        <h2
          style={{
            marginBottom: "1rem",
            fontSize: "1.5rem",
            fontWeight: 600,
          }}
        >
          {title}
        </h2>
      )}
      {children}
    </div>
  );
}
